const router = require("express").Router();
const multer = require("multer");
const { authenticateJWT } = require("../../middleware/Validator");
const {
  createPost,
  editPost,
  deletePost,
  getAllFeeds,
  toggleLike,
  addComment,
  editComment,
  deleteComment,
  getPostComments,
  generatePostShareUrl,
  getPublicPostDetails
} = require("../controller/feed/feed");

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit for videos
  },
  fileFilter: (req, file, cb) => {
    // Allow images and videos
    if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image and video files are allowed'), false);
    }
  }
});

// Feed Post Routes
router.post("/create_post", authenticateJWT, upload.single("media"), createPost);
router.put("/edit_post/:postId", authenticateJWT, upload.single("media"), editPost);
router.delete("/delete_post/:postId", authenticateJWT, deletePost);

// Feed Display Routes
router.post("/get_all_feeds", authenticateJWT, getAllFeeds);

// Like Routes
router.post("/toggle_like/:postId", authenticateJWT, toggleLike);

// Comment Routes
router.post("/add_comment/:postId", authenticateJWT, addComment);
router.put("/edit_comment/:commentId", authenticateJWT, editComment);
router.delete("/delete_comment/:commentId", authenticateJWT, deleteComment);
router.get("/get_comments/:postId", authenticateJWT, getPostComments);

// Share Routes
router.post("/generate_share_url", authenticateJWT, generatePostShareUrl);

// Public Routes (no authentication required)
router.post("/get_public_post_details", getPublicPostDetails);

module.exports = router;