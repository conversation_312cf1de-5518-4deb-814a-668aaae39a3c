import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Icon } from '@iconify/react';
import { loadStripe } from '@stripe/stripe-js';
import { processPayment } from '../../../services/userService';
import NoData from '../../../components/common/NoData';

// Helper function to get currency symbol
const getCurrencySymbol = (currency) => {
  switch (currency?.toUpperCase()) {
    case 'INR':
      return '₹';
    case 'USD':
      return '$';
    case 'SGD':
      return 'S$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    default:
      return '$'; // Default to USD symbol
  }
};

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);

function OrderDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const courseData = location.state;

  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);

  useEffect(() => {
    // Validate and log the received data
    console.log('OrderDetails - Received State:', location.state);
    
    if (!location.state || !location.state.course_id) {
      console.error('No course data or course ID found');
      // Optionally redirect back or show error
      // navigate(-1);
    }

    

    // Log all the course data
    console.log('Order Details - Course Data:', {
      course_id: courseData?.course_id,
      course_name: courseData?.course_name,
      course_price: courseData?.course_price,
      currency: courseData?.currency,
      banner_image: courseData?.banner_image,
      course_type: courseData?.course_type,
      course_desc: courseData?.course_desc,
      discountCode: courseData?.discountCode,
      discountValue: courseData?.discountValue,
      points: courseData?.points
    });
  }, [location.state]);

  const subtotal = Number(courseData?.course_price) || 0;
  const discount = Number(courseData?.discountValue) || 0;
  const total = Math.max(0, subtotal - discount);

  const handleCheckout = async () => {
    if (total <= 0) {
      toast.error('Total must be greater than $0');
      return;
    }

    setIsCheckoutLoading(true);
    try {
      const stripe = await stripePromise;
      if (!stripe) {
        toast.error('Stripe failed to initialize');
        setIsCheckoutLoading(false);
        return;
      }

      const response = await processPayment({
        amount: total * 100, // in cents
        currency: courseData.currency,
        paymentMethodId: 'pm_card_visa',
        origin: window.location.origin,
        courseName: courseData.course_name,
        points: courseData.points,
        course_id: courseData.course_id, // ✅ Make sure this is present
      });
      
      

      if (response.success && response.sessionUrl) {
        window.location.href = response.sessionUrl;
      } else {
        toast.error(response.error_msg || 'Payment failed. Try again.');
        setIsCheckoutLoading(false);
      }
    } catch (error) {
      console.error('Checkout Error:', error);
      toast.error('Failed to process payment.');
      setIsCheckoutLoading(false);
    }
  };

  if (!courseData || !courseData.course_id) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
        <div className="text-center">
          <div className="mb-4">
            <Icon icon="fluent:shopping-cart-24-regular" className="text-muted" style={{ fontSize: '4rem' }} />
          </div>
          <h3 className="text-muted mb-3">No order data available</h3>
          <p className="text-muted mb-4">Please select a course to purchase</p>
          <button 
            className="btn btn-primary px-4 py-2 rounded-pill"
            onClick={() => navigate(-1)}
          >
            <Icon icon="fluent:arrow-left-24-regular" className="me-2" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-5">
      <div className="row justify-content-center">
        <div className="col-lg-8">
          {/* Header */}
          <div className="text-left mb-3">
            <h1 className="display-6 fw-bold text-dark mb-2">Order Details</h1>
            <div className="d-flex justify-content-left align-items-center">
              <div className="bg-primary rounded-pill px-3 py-1">
                <small className="text-white fw-medium">
                  <Icon icon="fluent:lock-24-regular" className="me-1" />
                  Secure Payment Gateway
                </small>
              </div>
            </div>
          </div>

          {/* Single Card Design */}
          <div className="card" style={{ borderRadius: '20px' }}>
            <div className="card-body">
              {/* Course Information Section */}
              <div className="row mb-5">
                <div className="col-md-4 text-center">
                  <div className="position-relative mb-3">
                    <img
                      src={courseData.banner_image}
                      alt={courseData.course_name}
                      className="img-fluid rounded-3"
                      style={{ 
                        height: '200px', 
                        width: '100%', 
                        objectFit: 'cover',
                        boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
                      }}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '/placeholder-course-image.jpg';
                      }}
                    />
                    <div className="position-absolute top-0 end-0 m-2">
                      <span className="badge bg-primary px-2 py-1 rounded-pill">
                        <Icon icon="fluent:graduation-cap-24-regular" className="me-1" />
                        {courseData.course_type}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="col-md-8">
                  <h3 className="fw-bold text-dark mb-3">{courseData.course_name}</h3>
                  <p className="text-muted mb-4" style={{ 
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>{courseData.course_desc}</p>
                </div>
              </div>

              {/* Price Information */}
              <div className="row mb-4">
                <div className="col-6">
                  <div className="bg-light rounded-3 p-3 text-center">
                    <small className="text-muted d-block">Course Price</small>
                    <span className="h5 fw-bold text-primary mb-0">{getCurrencySymbol(courseData?.currency)}{subtotal.toFixed(2)}</span>
                  </div>
                </div>
                <div className="col-6">
                  <div className="bg-success bg-opacity-10 rounded-3 p-3 text-center">
                    <small className="text-muted d-block">Final Price</small>
                    <span className="h5 fw-bold text-success mb-0">{getCurrencySymbol(courseData?.currency)}{total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <hr className="my-4" />

              {/* Payment Summary Section */}
              <div className="mb-4">
                <div className="d-flex align-items-center mb-4">
                  <div className="bg-opacity-10 rounded-circle p-1 me-3 border border-primary">
                    <Icon icon="mdi:credit-card" className="fs-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="fw-bold text-dark mb-1">Payment Summary</h4>
                    <p className="text-muted mb-0">Complete your purchase securely</p>
                  </div>
                </div>

                <div className="payment-breakdown mb-4">
                  <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                    <div className="d-flex align-items-center">
                      <Icon icon="fluent:book-24-regular" className="text-primary me-2" />
                      <span className="fw-medium">Course Price</span>
                    </div>
                    <span className="fw-bold">{getCurrencySymbol(courseData?.currency)}{subtotal.toFixed(2)}</span>
                  </div>

                  {discount > 0 && (
                    <div className="d-flex justify-content-between align-items-center py-3 border-bottom text-success">
                      <div className="d-flex align-items-center">
                        <Icon icon="fluent:tag-24-regular" className="text-success me-2" />
                        <span className="fw-medium">Discount ({courseData.discountCode})</span>
                      </div>
                      <span className="fw-bold">-{getCurrencySymbol(courseData?.currency)}{discount.toFixed(2)}</span>
                    </div>
                  )}

                  {courseData.points > 0 && (
                    <div className="d-flex justify-content-between align-items-center py-3 border-bottom text-info">
                      <div className="d-flex align-items-center">
                        <Icon icon="fluent:coins-24-regular" className="text-info me-2" />
                        <span className="fw-medium">Points Used</span>
                      </div>
                      <span className="fw-bold">{courseData.points} points</span>
                    </div>
                  )}
                </div>

                <div className="payment-actions">
                  <button
                    className="btn btn-primary btn-lg w-100 py-3 rounded-pill shadow-sm"
                    onClick={handleCheckout}
                    disabled={isCheckoutLoading || total <= 0}
                    style={{ 
                      fontSize: '1.1rem',
                      fontWeight: '600',
                      background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                      border: 'none'
                    }}
                  >
                    {isCheckoutLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Processing Payment...
                      </>
                    ) : (
                      <>
                        <Icon icon="fluent:lock-24-regular" className="me-2" />
                        Pay {getCurrencySymbol(courseData?.currency)}{total.toFixed(2)} Securely
                      </>
                    )}
                  </button>

                  <div className="text-center mt-3">
                    <small className="text-muted">
                      <Icon icon="fluent:shield-24-regular" className="me-1" />
                      Your payment information is secure and encrypted
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderDetails;
