{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\Feed.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport FeedPost from './FeedPost.jsx';\nimport { getAllFeeds, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move ActionButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ActionButton = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  icon,\n  count,\n  onClick,\n  isLiked,\n  isLast,\n  buttonStyle,\n  actionButtonStyle\n}) => {\n  _s();\n  const buttonClass = useMemo(() => `btn border ${isLiked ? 'text-danger' : 'text-muted'}`, [isLiked]);\n  const buttonStyleMemo = useMemo(() => isLast ? {\n    ...actionButtonStyle,\n    marginRight: 0\n  } : actionButtonStyle, [isLast, actionButtonStyle]);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: buttonClass,\n    onClick: onClick,\n    style: buttonStyleMemo,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        icon: icon,\n        style: {\n          fontSize: '1.2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), count && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ms-1\",\n        style: {\n          fontSize: '0.9rem'\n        },\n        children: count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}, \"BW3KalXNlo6xULmJhXkh9aylRuM=\")), \"BW3KalXNlo6xULmJhXkh9aylRuM=\");\n_c2 = ActionButton;\nconst Feed = () => {\n  _s2();\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postingNewPost, setPostingNewPost] = useState(false);\n\n  // Comments state\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\n  const [userProfile, setUserProfile] = useState(null);\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\n\n  // Load initial feeds\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) setLoading(true);else {\n        setLoadingMore(true);\n        setShowLoadingAnimation(true);\n      }\n      const response = await getAllFeeds(page, 5);\n      console.log('Get all feeds response ------------------------', response);\n      if (response.success) {\n        const newPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          comments: [],\n          // Comments will be loaded separately\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n        if (append) {\n          // Add 1 second delay for smooth loading animation\n          setTimeout(() => {\n            setPosts(prev => [...prev, ...newPosts]);\n            setLoadingMore(false);\n            setShowLoadingAnimation(false);\n          }, 1000);\n        } else {\n          setPosts(newPosts);\n          setLoading(false);\n        }\n        setHasMore(response.data.pagination.has_more);\n        setCurrentPage(page);\n\n        // Set user profile if available\n        if (response.data.user_profile) {\n          setUserProfile(response.data.user_profile);\n        }\n      } else {\n        toast.error('Failed to load feeds');\n        setLoadingMore(false);\n        setShowLoadingAnimation(false);\n      }\n    } catch (error) {\n      console.error('Error loading feeds:', error);\n      toast.error('Failed to load feeds');\n      setLoadingMore(false);\n      setShowLoadingAnimation(false);\n    } finally {\n      if (!append) {\n        setLoading(false);\n      }\n    }\n  }, []);\n\n  // Load more posts for infinite scroll\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', {\n        currentPage: currentPage + 1,\n        hasMore\n      });\n      loadFeeds(currentPage + 1, true);\n    }\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        console.log('Scrolled to bottom, checking if should load more...', {\n          loadingMore,\n          hasMore,\n          currentPage\n        });\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadMorePosts, loadingMore, hasMore]);\n\n  // Initial load\n  useEffect(() => {\n    loadFeeds();\n  }, [loadFeeds]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Event handlers\n  const handleLike = async postId => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          isLiked: response.data.is_liked,\n          likes: response.data.likes_count\n        } : post));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      } else {\n        setLoadingMoreComments(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      }\n      const response = await getPostComments(postId, page, 10);\n      console.log('Get post comments response ------------------------', response);\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString(),\n          user_id: comment.user_id // Add user_id for permission checks\n        }));\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n        setCommentsPage(prev => ({\n          ...prev,\n          [postId]: page\n        }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n      setLoadingMoreComments(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n    }\n  }, []);\n  const handleComment = postId => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({\n      ...prev,\n      [postId]: !prev[postId]\n    }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n  const loadMoreComments = postId => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n\n  // Infinite scroll for comments\n  const handleCommentsScroll = (postId, e) => {\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = e.target;\n\n    // Check if scrolled to bottom (with 50px threshold)\n    if (scrollTop + clientHeight >= scrollHeight - 50) {\n      const hasMore = commentsHasMore[postId];\n      const isLoading = loadingMoreComments[postId];\n      if (hasMore && !isLoading) {\n        console.log('Scrolled to bottom of comments, loading more...', {\n          postId\n        });\n        loadMoreComments(postId);\n      }\n    }\n  };\n  const handleSubmitComment = async postId => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    if (commentText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount + 1\n        } : post));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now',\n          user_id: user_id // Add user_id for permission checks\n        };\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n        setNewComment(prev => ({\n          ...prev,\n          [postId]: ''\n        }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handleEditComment = async (postId, commentId) => {\n    const editText = editCommentText[commentId];\n    if (!editText || !editText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    if (editText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await editComment(commentId, editText.trim());\n      if (response.success) {\n        // Update the comment in the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].map(comment => comment.id === commentId ? {\n            ...comment,\n            text: editText.trim()\n          } : comment)\n        }));\n        setEditingComment(prev => ({\n          ...prev,\n          [commentId]: false\n        }));\n        setEditCommentText(prev => ({\n          ...prev,\n          [commentId]: ''\n        }));\n        toast.success('Comment updated successfully');\n      } else {\n        toast.error('Failed to update comment');\n      }\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n    }\n  };\n  const handleDeleteComment = async (postId, commentId) => {\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\n      return;\n    }\n    try {\n      const response = await deleteComment(commentId);\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount - 1\n        } : post));\n\n        // Remove the comment from the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\n        }));\n        toast.success('Comment deleted successfully');\n      } else {\n        toast.error('Failed to delete comment');\n      }\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n    }\n  };\n  const handlePostSubmit = async newPost => {\n    console.log('handlePostSubmit called with:', newPost);\n\n    // Show loading state\n    setPostingNewPost(true);\n\n    // Instead of creating a fake post, let's refresh the feed to get the real data\n    setTimeout(async () => {\n      try {\n        // Refresh the feed to get the latest posts including the new one\n        await loadFeeds(1, false);\n        setPostingNewPost(false);\n      } catch (error) {\n        console.error('Error refreshing feed after post creation:', error);\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n  const handleMyFeedClick = () => {\n    navigate('/user/my-feed');\n  };\n  const handleShare = async post => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    const mediaStyle = {\n      width: '100%',\n      maxHeight: '400px'\n    };\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"img\", {\n        src: media.url,\n        className: \"img-fluid rounded\",\n        alt: \"Post media\",\n        style: {\n          ...mediaStyle,\n          objectFit: 'contain'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 14\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"video\", {\n        className: \"img-fluid rounded\",\n        controls: true,\n        style: mediaStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: media.url,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text mb-2\",\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this);\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"card-text mb-2\",\n        children: [displayText, shouldTruncate && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-link p-0 ms-2 text-primary text-decoration-none\",\n          onClick: () => setShowFullText(prev => ({\n            ...prev,\n            [post.id]: !isShowingFull\n          })),\n          children: isShowingFull ? 'Show less' : 'Show more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({\n      ...prev,\n      [postId]: value\n    }));\n  }, []);\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, []);\n  const handleCommentSubmitClick = useCallback(postId => {\n    handleSubmitComment(postId);\n  }, []);\n  const handleEditCommentChange = useCallback((commentId, value) => {\n    setEditCommentText(prev => ({\n      ...prev,\n      [commentId]: value\n    }));\n  }, []);\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\n    if (e.key === 'Enter') {\n      handleEditComment(postId, commentId);\n    }\n  }, []);\n  const renderComments = post => {\n    if (!showComments[post.id]) return null;\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-top pt-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-3\",\n        children: [\"Comments (\", post.commentsCount || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: DefaultProfile,\n          className: \"rounded-circle me-2\",\n          alt: \"Profile\",\n          style: {\n            width: '32px',\n            height: '32px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Write a comment...\",\n            value: newComment[post.id] || '',\n            onChange: e => handleCommentChange(post.id, e.target.value),\n            onKeyDown: e => handleCommentKeyDown(e, post.id),\n            maxLength: 400\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-1\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: (newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n              children: [(newComment[post.id] || '').length, \"/400 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm ms-2 w-auto\",\n          onClick: () => handleCommentSubmitClick(post.id),\n          disabled: !newComment[post.id] || !newComment[post.id].trim(),\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border spinner-border-sm\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted small\",\n          children: \"Loading comments...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          id: `comments-container-${post.id}`,\n          onScroll: e => handleCommentsScroll(post.id, e),\n          children: [comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: comment.avatar,\n              className: \"rounded-circle me-2\",\n              alt: comment.user,\n              style: {\n                width: '32px',\n                height: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-light rounded p-2 flex-grow-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold\",\n                  children: comment.user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 23\n                }, this), comment.user_id === user_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-1\",\n                  children: editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-success\",\n                      onClick: () => handleEditComment(post.id, comment.id),\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:check\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-secondary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: false\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: ''\n                        }));\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:close\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 623,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-primary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: true\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: comment.text\n                        }));\n                      },\n                      title: \"Edit comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:pencil\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-danger\",\n                      onClick: () => handleDeleteComment(post.id, comment.id),\n                      title: \"Delete comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:delete\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 21\n              }, this), editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control form-control-sm\",\n                  value: editCommentText[comment.id] || '',\n                  onChange: e => handleEditCommentChange(comment.id, e.target.value),\n                  onKeyDown: e => handleEditCommentKeyDown(e, post.id, comment.id),\n                  maxLength: 400,\n                  autoFocus: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-end mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: (editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n                    children: [(editCommentText[comment.id] || '').length, \"/400 characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: comment.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-muted small mt-1\",\n                children: comment.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 19\n            }, this)]\n          }, comment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 17\n          }, this)), isLoadingMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-2 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm text-muted\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more comments...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ms-2 text-muted small\",\n              children: \"Loading more comments...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes fadeIn {\n            from {\n              opacity: 0;\n              transform: translateY(-10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          @keyframes slideInDown {\n            from {\n              opacity: 0;\n              transform: translateY(-30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          .card {\n            transition: all 0.3s ease-in-out;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            onClick: handleMyFeedClick,\n            style: {\n              cursor: 'pointer',\n              padding: '8px',\n              borderRadius: '8px',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseEnter: e => e.currentTarget.style.backgroundColor = '#f8f9fa',\n            onMouseLeave: e => e.currentTarget.style.backgroundColor = 'transparent',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-end me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Click to view your feed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n              className: \"rounded-circle\",\n              alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"User Profile\",\n              style: {\n                width: '50px',\n                height: '50px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeedPost, {\n          onPostSubmit: handlePostSubmit,\n          userProfile: userProfile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this), postingNewPost && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          style: {\n            animation: 'fadeIn 0.5s ease-in-out',\n            border: '2px dashed #007bff',\n            backgroundColor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-3\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Creating post...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-2\",\n              children: \"Creating your post...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Please wait while we process your content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"Loading posts...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 13\n        }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:post-outline\",\n            style: {\n              fontSize: '3rem',\n              color: '#6c757d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"No posts yet. Be the first to share something!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [posts.map((post, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-4\",\n            style: {\n              animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\n              transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: post.user.avatar,\n                  className: \"rounded-circle me-3\",\n                  alt: post.user.name,\n                  style: {\n                    width: '40px',\n                    height: '40px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: post.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: new Date(post.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [renderPostContent(post.content, post), renderMedia(post.media)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\",\n                  count: post.likes,\n                  onClick: () => handleLike(post.id),\n                  isLiked: post.isLiked,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:comment-outline\",\n                  count: post.commentsCount || 0,\n                  onClick: () => handleComment(post.id),\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:share-variant-outline\",\n                  onClick: () => handleShare(post),\n                  isLast: true,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 21\n              }, this), renderComments(post)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 19\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 17\n          }, this)), showLoadingAnimation && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            style: {\n              animation: 'fadeIn 0.5s ease-in-out'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more posts...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-primary mb-0\",\n              children: \"Loading more posts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 17\n          }, this), !showLoadingAnimation && !loadingMore && hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline-primary\",\n              onClick: loadMorePosts,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"mdi:chevron-down\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 21\n              }, this), \"Load More Posts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 17\n          }, this), !hasMore && posts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"You've reached the end of the feed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 696,\n    columnNumber: 5\n  }, this);\n};\n_s2(Feed, \"xo8yIJX5mCDwSCgFEnXzyjK2lZY=\", false, function () {\n  return [useNavigate];\n});\n_c3 = Feed;\nexport default Feed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ActionButton$React.memo\");\n$RefreshReg$(_c2, \"ActionButton\");\n$RefreshReg$(_c3, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Icon", "useNavigate", "DefaultProfile", "FeedPost", "getAllFeeds", "toggleLike", "addComment", "getPostComments", "editComment", "deleteComment", "generatePostShareUrl", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ActionButton", "_s", "memo", "_c", "icon", "count", "onClick", "isLiked", "isLast", "buttonStyle", "actionButtonStyle", "buttonClass", "buttonStyleMemo", "marginRight", "className", "style", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "Feed", "_s2", "navigate", "posts", "setPosts", "loading", "setLoading", "loadingMore", "setLoadingMore", "currentPage", "setCurrentPage", "hasMore", "setHasMore", "showLoadingAnimation", "setShowLoadingAnimation", "newComment", "setNewComment", "showComments", "setShowComments", "postingNewPost", "setPostingNewPost", "postComments", "setPostComments", "commentsLoading", "setCommentsLoading", "commentsPage", "setCommentsPage", "commentsHasMore", "setCommentsHasMore", "loadingMoreComments", "setLoadingMoreComments", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "userProfile", "setUserProfile", "showFullText", "setShowFullText", "user_id", "JSON", "parse", "localStorage", "getItem", "id", "loadFeeds", "page", "append", "response", "console", "log", "success", "newPosts", "data", "map", "post", "user", "name", "user_name", "avatar", "user_avatar", "content", "description", "media", "media_url", "type", "media_type", "url", "is_liked_by_user", "likes", "likes_count", "comments", "commentsCount", "comments_count", "created_at", "setTimeout", "prev", "pagination", "has_more", "user_profile", "error", "loadMorePosts", "handleScroll", "scrollTop", "document", "documentElement", "scrollHeight", "clientHeight", "window", "addEventListener", "removeEventListener", "backgroundColor", "borderColor", "flex", "handleLike", "postId", "is_liked", "loadPostComments", "newComments", "comment", "text", "timestamp", "Date", "commented_at", "toLocaleDateString", "handleComment", "isOpening", "loadMoreComments", "handleCommentsScroll", "e", "target", "isLoading", "handleSubmitComment", "commentText", "trim", "length", "newCommentObj", "handleEditComment", "commentId", "editText", "handleDeleteComment", "confirm", "filter", "handlePostSubmit", "newPost", "handleMyFeedClick", "handleShare", "shareUrl", "shareData", "title", "navigator", "share", "clipboard", "writeText", "renderMedia", "mediaStyle", "width", "maxHeight", "src", "alt", "objectFit", "controls", "renderPostContent", "hasMedia", "shouldTruncate", "isShowingFull", "displayText", "substring", "handleCommentChange", "value", "handleCommentKeyDown", "key", "handleCommentSubmitClick", "handleEditCommentChange", "handleEditCommentKeyDown", "renderComments", "isLoadingMore", "height", "placeholder", "onChange", "onKeyDown", "max<PERSON><PERSON><PERSON>", "disabled", "role", "overflowY", "onScroll", "autoFocus", "cursor", "padding", "borderRadius", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "profile_pic_url", "onPostSubmit", "animation", "border", "color", "index", "transform", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/Feed.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react'\nimport { Icon } from '@iconify/react'\nimport { useNavigate } from 'react-router-dom'\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\nimport FeedPost from './FeedPost.jsx'\nimport { getAllFeeds, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices'\nimport { toast } from 'react-toastify'\n\n// Move ActionButton outside to prevent recreation on every render\nconst ActionButton = React.memo(({ icon, count, onClick, isLiked, isLast, buttonStyle, actionButtonStyle }) => {\n  const buttonClass = useMemo(() =>\n    `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,\n    [isLiked]\n  );\n\n  const buttonStyleMemo = useMemo(() =>\n    isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle,\n    [isLast, actionButtonStyle]\n  );\n\n  return (\n    <button\n      className={buttonClass}\n      onClick={onClick}\n      style={buttonStyleMemo}\n    >\n      <div className=\"d-flex align-items-center justify-content-center\">\n        <Icon icon={icon} style={{fontSize: '1.2rem'}} />\n        {count && <span className=\"ms-1\" style={{fontSize: '0.9rem'}}>{count}</span>}\n      </div>\n    </button>\n  );\n});\n\nconst Feed = () => {\n  const navigate = useNavigate();\n\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postingNewPost, setPostingNewPost] = useState(false);\n\n  // Comments state\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\n  const [userProfile, setUserProfile] = useState(null);\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\n\n  // Load initial feeds\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) setLoading(true);\n      else {\n        setLoadingMore(true);\n        setShowLoadingAnimation(true);\n      }\n\n      const response = await getAllFeeds(page, 5);\n      console.log('Get all feeds response ------------------------', response);\n\n      if (response.success) {\n        const newPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          comments: [], // Comments will be loaded separately\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n\n        if (append) {\n          // Add 1 second delay for smooth loading animation\n          setTimeout(() => {\n            setPosts(prev => [...prev, ...newPosts]);\n            setLoadingMore(false);\n            setShowLoadingAnimation(false);\n          }, 1000);\n        } else {\n          setPosts(newPosts);\n          setLoading(false);\n        }\n\n        setHasMore(response.data.pagination.has_more);\n        setCurrentPage(page);\n        \n        // Set user profile if available\n        if (response.data.user_profile) {\n          setUserProfile(response.data.user_profile);\n        }\n      } else {\n        toast.error('Failed to load feeds');\n        setLoadingMore(false);\n        setShowLoadingAnimation(false);\n      }\n    } catch (error) {\n      console.error('Error loading feeds:', error);\n      toast.error('Failed to load feeds');\n      setLoadingMore(false);\n      setShowLoadingAnimation(false);\n    } finally {\n      if (!append) {\n        setLoading(false);\n      }\n    }\n  }, []);\n\n  // Load more posts for infinite scroll\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });\n      loadFeeds(currentPage + 1, true);\n    }\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n      \n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        console.log('Scrolled to bottom, checking if should load more...', {\n          loadingMore,\n          hasMore,\n          currentPage\n        });\n        \n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadMorePosts, loadingMore, hasMore]);\n\n  // Initial load\n  useEffect(() => {\n    loadFeeds();\n  }, [loadFeeds]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Event handlers\n  const handleLike = async (postId) => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? {\n                ...post,\n                isLiked: response.data.is_liked,\n                likes: response.data.likes_count\n              }\n            : post\n        ));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({ ...prev, [postId]: true }));\n      } else {\n        setLoadingMoreComments(prev => ({ ...prev, [postId]: true }));\n      }\n\n      const response = await getPostComments(postId, page, 10);\n\n      console.log('Get post comments response ------------------------', response);\n\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString(),\n          user_id: comment.user_id // Add user_id for permission checks\n        }));\n\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n\n        setCommentsPage(prev => ({ ...prev, [postId]: page }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({ ...prev, [postId]: false }));\n      setLoadingMoreComments(prev => ({ ...prev, [postId]: false }));\n    }\n  }, []);\n\n  const handleComment = (postId) => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n\n  const loadMoreComments = (postId) => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n\n  // Infinite scroll for comments\n  const handleCommentsScroll = (postId, e) => {\n    const { scrollTop, scrollHeight, clientHeight } = e.target;\n    \n    // Check if scrolled to bottom (with 50px threshold)\n    if (scrollTop + clientHeight >= scrollHeight - 50) {\n      const hasMore = commentsHasMore[postId];\n      const isLoading = loadingMoreComments[postId];\n      \n      if (hasMore && !isLoading) {\n        console.log('Scrolled to bottom of comments, loading more...', { postId });\n        loadMoreComments(postId);\n      }\n    }\n  };\n\n  const handleSubmitComment = async (postId) => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n\n    if (commentText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? { ...post, commentsCount: post.commentsCount + 1 }\n            : post\n        ));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now',\n          user_id: user_id // Add user_id for permission checks\n        };\n\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n\n  const handleEditComment = async (postId, commentId) => {\n    const editText = editCommentText[commentId];\n    if (!editText || !editText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n\n    if (editText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n\n    try {\n      const response = await editComment(commentId, editText.trim());\n      if (response.success) {\n        // Update the comment in the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].map(comment =>\n            comment.id === commentId\n              ? { ...comment, text: editText.trim() }\n              : comment\n          )\n        }));\n\n        setEditingComment(prev => ({ ...prev, [commentId]: false }));\n        setEditCommentText(prev => ({ ...prev, [commentId]: '' }));\n        toast.success('Comment updated successfully');\n      } else {\n        toast.error('Failed to update comment');\n      }\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n    }\n  };\n\n  const handleDeleteComment = async (postId, commentId) => {\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\n      return;\n    }\n\n    try {\n      const response = await deleteComment(commentId);\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? { ...post, commentsCount: post.commentsCount - 1 }\n            : post\n        ));\n\n        // Remove the comment from the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\n        }));\n\n        toast.success('Comment deleted successfully');\n      } else {\n        toast.error('Failed to delete comment');\n      }\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n    }\n  };\n\n  const handlePostSubmit = async (newPost) => {\n    console.log('handlePostSubmit called with:', newPost);\n    \n    // Show loading state\n    setPostingNewPost(true);\n    \n    // Instead of creating a fake post, let's refresh the feed to get the real data\n    setTimeout(async () => {\n      try {\n        // Refresh the feed to get the latest posts including the new one\n        await loadFeeds(1, false);\n        setPostingNewPost(false);\n      } catch (error) {\n        console.error('Error refreshing feed after post creation:', error);\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n\n\n\n  const handleMyFeedClick = () => {\n    navigate('/user/my-feed');\n  };\n\n  const handleShare = async (post) => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = (media) => {\n    if (!media) return null;\n\n    const mediaStyle = { width: '100%', maxHeight: '400px' };\n\n    if (media.type === 'image') {\n      return <img src={media.url} className=\"img-fluid rounded\" alt=\"Post media\" style={{...mediaStyle, objectFit: 'contain'}} />;\n    } else if (media.type === 'video') {\n      return (\n        <video className=\"img-fluid rounded\" controls style={mediaStyle}>\n          <source src={media.url} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n      );\n    }\n    return null;\n  };\n\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return (\n        <div>\n          <p className=\"card-text mb-2\">{content}</p>\n        </div>\n      );\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n\n    return (\n      <div>\n        <p className=\"card-text mb-2\">\n          {displayText}\n          {shouldTruncate && (\n            <button\n              className=\"btn btn-link p-0 ms-2 text-primary text-decoration-none\"\n              onClick={() => setShowFullText(prev => ({ ...prev, [post.id]: !isShowingFull }))}\n            >\n              {isShowingFull ? 'Show less' : 'Show more'}\n            </button>\n          )}\n        </p>\n      </div>\n    );\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({ ...prev, [postId]: value }));\n  }, []);\n\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, []);\n\n  const handleCommentSubmitClick = useCallback((postId) => {\n    handleSubmitComment(postId);\n  }, []);\n\n  const handleEditCommentChange = useCallback((commentId, value) => {\n    setEditCommentText(prev => ({ ...prev, [commentId]: value }));\n  }, []);\n\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\n    if (e.key === 'Enter') {\n      handleEditComment(postId, commentId);\n    }\n  }, []);\n\n  const renderComments = (post) => {\n    if (!showComments[post.id]) return null;\n\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n\n    return (\n      <div className=\"border-top pt-3 mt-3\">\n        <h6 className=\"mb-3\">Comments ({post.commentsCount || 0})</h6>\n\n        {/* Comment Input */}\n        <div className=\"d-flex mb-3\">\n          <img src={DefaultProfile} className=\"rounded-circle me-2\" alt=\"Profile\" style={{width: '32px', height: '32px'}} />\n          <div className=\"flex-grow-1\">\n            <input\n              type=\"text\"\n              className=\"form-control\"\n              placeholder=\"Write a comment...\"\n              value={newComment[post.id] || ''}\n              onChange={(e) => handleCommentChange(post.id, e.target.value)}\n              onKeyDown={(e) => handleCommentKeyDown(e, post.id)}\n              maxLength={400}\n            />\n            <div className=\"d-flex justify-content-end mt-1\">\n              <small className={(newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\n                {(newComment[post.id] || '').length}/400 characters\n              </small>\n            </div>\n          </div>\n          <button\n            className=\"btn btn-primary btn-sm ms-2 w-auto\"\n            onClick={() => handleCommentSubmitClick(post.id)}\n            disabled={!newComment[post.id] || !newComment[post.id].trim()}\n          >\n            <Icon icon=\"mdi:send\" />\n          </button>\n        </div>\n\n        {/* Comments Loading State */}\n        {isLoading ? (\n          <div className=\"text-center py-3\">\n            <div className=\"spinner-border spinner-border-sm\" role=\"status\">\n              <span className=\"visually-hidden\">Loading comments...</span>\n            </div>\n            <p className=\"mt-2 text-muted small\">Loading comments...</p>\n          </div>\n        ) : (\n          <>\n            {/* Comments Container with Scroll */}\n            <div \n              style={{ maxHeight: '300px', overflowY: 'auto' }} \n              id={`comments-container-${post.id}`}\n              onScroll={(e) => handleCommentsScroll(post.id, e)}\n            >\n              {/* Existing Comments */}\n              {comments.map(comment => (\n                <div key={comment.id} className=\"d-flex mb-2\">\n                  <img src={comment.avatar} className=\"rounded-circle me-2\" alt={comment.user} style={{width: '32px', height: '32px'}} />\n                  <div className=\"bg-light rounded p-2 flex-grow-1\">\n                    <div className=\"d-flex justify-content-between align-items-start\">\n                      <div className=\"fw-bold\">{comment.user}</div>\n                      {/* Show edit/delete options only for user's own comments */}\n                      {comment.user_id === user_id && (\n                        <div className=\"d-flex gap-1\">\n                          {editingComment[comment.id] ? (\n                            <>\n                              <button\n                                className=\"btn btn-sm btn-success\"\n                                onClick={() => handleEditComment(post.id, comment.id)}\n                              >\n                                <Icon icon=\"mdi:check\" style={{fontSize: '0.8rem'}} />\n                              </button>\n                              <button\n                                className=\"btn btn-sm btn-secondary\"\n                                onClick={() => {\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: false }));\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: '' }));\n                                }}\n                              >\n                                <Icon icon=\"mdi:close\" style={{fontSize: '0.8rem'}} />\n                              </button>\n                            </>\n                          ) : (\n                            <>\n                              <button\n                                className=\"btn btn-sm btn-outline-primary\"\n                                onClick={() => {\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: true }));\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: comment.text }));\n                                }}\n                                title=\"Edit comment\"\n                              >\n                                <Icon icon=\"mdi:pencil\" style={{fontSize: '0.8rem'}} />\n                              </button>\n                              <button\n                                className=\"btn btn-sm btn-outline-danger\"\n                                onClick={() => handleDeleteComment(post.id, comment.id)}\n                                title=\"Delete comment\"\n                              >\n                                <Icon icon=\"mdi:delete\" style={{fontSize: '0.8rem'}} />\n                              </button>\n                            </>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                    \n                    {editingComment[comment.id] ? (\n                      <div className=\"mt-2\">\n                        <input\n                          type=\"text\"\n                          className=\"form-control form-control-sm\"\n                          value={editCommentText[comment.id] || ''}\n                          onChange={(e) => handleEditCommentChange(comment.id, e.target.value)}\n                          onKeyDown={(e) => handleEditCommentKeyDown(e, post.id, comment.id)}\n                          maxLength={400}\n                          autoFocus\n                        />\n                        <div className=\"d-flex justify-content-end mt-1\">\n                          <small className={(editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\n                            {(editCommentText[comment.id] || '').length}/400 characters\n                          </small>\n                        </div>\n                      </div>\n                    ) : (\n                      <div>{comment.text}</div>\n                    )}\n                    \n                    <div className=\"text-muted small mt-1\">{comment.timestamp}</div>\n                  </div>\n                </div>\n              ))}\n\n              {/* Loading More Comments Indicator */}\n              {isLoadingMore && (\n                <div className=\"text-center mt-2 py-2\">\n                  <div className=\"spinner-border spinner-border-sm text-muted\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading more comments...</span>\n                  </div>\n                  <span className=\"ms-2 text-muted small\">Loading more comments...</span>\n                </div>\n              )}\n            </div>\n          </>\n        )}\n      </div>\n    );\n  };\n\n\n\n  return (\n    <div className=\"container py-4\">\n      <style>\n        {`\n          @keyframes fadeIn {\n            from {\n              opacity: 0;\n              transform: translateY(-10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          @keyframes slideInDown {\n            from {\n              opacity: 0;\n              transform: translateY(-30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          .card {\n            transition: all 0.3s ease-in-out;\n          }\n        `}\n      </style>\n      <div className=\"row justify-content-center\">\n        <div className=\"col-md-8\">\n          {/* Profile Header */}\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <div></div>\n            <div \n              className=\"d-flex align-items-center\"\n              onClick={handleMyFeedClick}\n              style={{ \n                cursor: 'pointer',\n                padding: '8px',\n                borderRadius: '8px',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}\n              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}\n            >\n              <div className=\"text-end me-3\">\n                <h5 className=\"mb-0\">{userProfile?.name || 'User'}</h5>\n                <small className=\"text-muted\">Click to view your feed</small>\n              </div>\n              <img \n                src={userProfile?.profile_pic_url || DefaultProfile} \n                className=\"rounded-circle\" \n                alt={userProfile?.name || \"User Profile\"} \n                style={{width: '50px', height: '50px'}} \n              />\n            </div>\n          </div>\n\n          {/* Create Post Component */}\n          <FeedPost onPostSubmit={handlePostSubmit} userProfile={userProfile} />\n\n          {/* New Post Loading State */}\n          {postingNewPost && (\n            <div className=\"card mb-4\" style={{\n              animation: 'fadeIn 0.5s ease-in-out',\n              border: '2px dashed #007bff',\n              backgroundColor: '#f8f9fa'\n            }}>\n              <div className=\"card-body text-center py-4\">\n                <div className=\"spinner-border text-primary mb-3\" role=\"status\">\n                  <span className=\"visually-hidden\">Creating post...</span>\n                </div>\n                <h6 className=\"text-primary mb-2\">Creating your post...</h6>\n                <p className=\"text-muted mb-0\">Please wait while we process your content</p>\n              </div>\n            </div>\n          )}\n\n          {/* Loading State */}\n          {loading ? (\n            <div className=\"text-center py-4\">\n              <div className=\"spinner-border\" role=\"status\">\n                <span className=\"visually-hidden\">Loading...</span>\n              </div>\n              <p className=\"mt-2 text-muted\">Loading posts...</p>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <Icon icon=\"mdi:post-outline\" style={{ fontSize: '3rem', color: '#6c757d' }} />\n              <p className=\"mt-2 text-muted\">No posts yet. Be the first to share something!</p>\n            </div>\n          ) : (\n            <>\n              {/* Posts Feed */}\n              {posts.map((post, index) => (\n                <div \n                  key={post.id} \n                  className=\"card mb-4\"\n                  style={{\n                    animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\n                    transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\n                  }}\n                >\n                  <div className=\"card-body\">\n                    {/* Post Header */}\n                    <div className=\"d-flex align-items-center mb-3\">\n                      <img src={post.user.avatar} className=\"rounded-circle me-3\" alt={post.user.name} style={{width: '40px', height: '40px'}} />\n                      <div className=\"flex-grow-1\">\n                        <h6 className=\"mb-0\">{post.user.name}</h6>\n                        <small className=\"text-muted\">{new Date(post.created_at).toLocaleDateString()}</small>\n                      </div>\n                    </div>\n\n                    {/* Post Content */}\n                    <div className=\"mb-3\">\n                      {renderPostContent(post.content, post)}\n                      {renderMedia(post.media)}\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className=\"d-flex justify-content-between\">\n                      <ActionButton\n                        icon={post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\"}\n                        count={post.likes}\n                        onClick={() => handleLike(post.id)}\n                        isLiked={post.isLiked}\n                        buttonStyle={buttonStyle}\n                        actionButtonStyle={actionButtonStyle}\n                      />\n                      <ActionButton\n                        icon=\"mdi:comment-outline\"\n                        count={post.commentsCount || 0}\n                        onClick={() => handleComment(post.id)}\n                        buttonStyle={buttonStyle}\n                        actionButtonStyle={actionButtonStyle}\n                      />\n                      <ActionButton\n                        icon=\"mdi:share-variant-outline\"\n                        onClick={() => handleShare(post)}\n                        isLast={true}\n                        buttonStyle={buttonStyle}\n                        actionButtonStyle={actionButtonStyle}\n                      />\n                    </div>\n\n                    {/* Comments Section */}\n                    {renderComments(post)}\n                  </div>\n                </div>\n              ))}\n\n              {/* Load More Button */}\n              {showLoadingAnimation && (\n                <div className=\"text-center py-4\" style={{\n                  animation: 'fadeIn 0.5s ease-in-out'\n                }}>\n                  <div className=\"spinner-border text-primary mb-2\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading more posts...</span>\n                  </div>\n                  <p className=\"text-primary mb-0\">Loading more posts...</p>\n                </div>\n              )}\n\n              {!showLoadingAnimation && !loadingMore && hasMore && (\n                <div className=\"text-center py-3\">\n                  <button \n                    className=\"btn btn-outline-primary\"\n                    onClick={loadMorePosts}\n                  >\n                    <Icon icon=\"mdi:chevron-down\" className=\"me-2\" />\n                    Load More Posts\n                  </button>\n                </div>\n              )}\n\n              {!hasMore && posts.length > 0 && (\n                <div className=\"text-center py-3\">\n                  <p className=\"text-muted\">You've reached the end of the feed!</p>\n                </div>\n              )}\n\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Feed;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,gCAAgC;AACvJ,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,gBAAAC,EAAA,cAAGtB,KAAK,CAACuB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,WAAW;EAAEC;AAAkB,CAAC,KAAK;EAAAT,EAAA;EAC7G,MAAMU,WAAW,GAAG5B,OAAO,CAAC,MAC1B,cAAcwB,OAAO,GAAG,aAAa,GAAG,YAAY,EAAE,EACtD,CAACA,OAAO,CACV,CAAC;EAED,MAAMK,eAAe,GAAG7B,OAAO,CAAC,MAC9ByB,MAAM,GAAG;IAAE,GAAGE,iBAAiB;IAAEG,WAAW,EAAE;EAAE,CAAC,GAAGH,iBAAiB,EACrE,CAACF,MAAM,EAAEE,iBAAiB,CAC5B,CAAC;EAED,oBACEb,OAAA;IACEiB,SAAS,EAAEH,WAAY;IACvBL,OAAO,EAAEA,OAAQ;IACjBS,KAAK,EAAEH,eAAgB;IAAAI,QAAA,eAEvBnB,OAAA;MAAKiB,SAAS,EAAC,kDAAkD;MAAAE,QAAA,gBAC/DnB,OAAA,CAACb,IAAI;QAACoB,IAAI,EAAEA,IAAK;QAACW,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChDhB,KAAK,iBAAIR,OAAA;QAAMiB,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ,CAAE;QAAAD,QAAA,EAAEX;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC,kCAAC;AAACC,GAAA,GAvBGtB,YAAY;AAyBlB,MAAMuB,IAAI,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjB,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtD,MAAMkF,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE;;EAE3D;EACA,MAAMC,SAAS,GAAGtF,WAAW,CAAC,OAAOuF,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAChE,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAExC,UAAU,CAAC,IAAI,CAAC,CAAC,KAC5B;QACHE,cAAc,CAAC,IAAI,CAAC;QACpBM,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEA,MAAMkC,QAAQ,GAAG,MAAMnF,WAAW,CAACiF,IAAI,EAAE,CAAC,CAAC;MAC3CG,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEF,QAAQ,CAAC;MAExE,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMC,QAAQ,GAAGJ,QAAQ,CAACK,IAAI,CAAClD,KAAK,CAACmD,GAAG,CAACC,IAAI,KAAK;UAChDX,EAAE,EAAEW,IAAI,CAACX,EAAE;UACXY,IAAI,EAAE;YACJC,IAAI,EAAEF,IAAI,CAACG,SAAS;YACpBC,MAAM,EAAEJ,IAAI,CAACK,WAAW,IAAIjG;UAC9B,CAAC;UACDkG,OAAO,EAAEN,IAAI,CAACO,WAAW;UACzBC,KAAK,EAAER,IAAI,CAACS,SAAS,GAAG;YACtBC,IAAI,EAAEV,IAAI,CAACW,UAAU;YACrBC,GAAG,EAAEZ,IAAI,CAACS;UACZ,CAAC,GAAG,IAAI;UACRhF,OAAO,EAAEuE,IAAI,CAACa,gBAAgB,KAAK,CAAC;UACpCC,KAAK,EAAEd,IAAI,CAACe,WAAW;UACvBC,QAAQ,EAAE,EAAE;UAAE;UACdC,aAAa,EAAEjB,IAAI,CAACkB,cAAc;UAClCC,UAAU,EAAEnB,IAAI,CAACmB;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI3B,MAAM,EAAE;UACV;UACA4B,UAAU,CAAC,MAAM;YACfvE,QAAQ,CAACwE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGxB,QAAQ,CAAC,CAAC;YACxC5C,cAAc,CAAC,KAAK,CAAC;YACrBM,uBAAuB,CAAC,KAAK,CAAC;UAChC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLV,QAAQ,CAACgD,QAAQ,CAAC;UAClB9C,UAAU,CAAC,KAAK,CAAC;QACnB;QAEAM,UAAU,CAACoC,QAAQ,CAACK,IAAI,CAACwB,UAAU,CAACC,QAAQ,CAAC;QAC7CpE,cAAc,CAACoC,IAAI,CAAC;;QAEpB;QACA,IAAIE,QAAQ,CAACK,IAAI,CAAC0B,YAAY,EAAE;UAC9B3C,cAAc,CAACY,QAAQ,CAACK,IAAI,CAAC0B,YAAY,CAAC;QAC5C;MACF,CAAC,MAAM;QACL3G,KAAK,CAAC4G,KAAK,CAAC,sBAAsB,CAAC;QACnCxE,cAAc,CAAC,KAAK,CAAC;QACrBM,uBAAuB,CAAC,KAAK,CAAC;MAChC;IACF,CAAC,CAAC,OAAOkE,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C5G,KAAK,CAAC4G,KAAK,CAAC,sBAAsB,CAAC;MACnCxE,cAAc,CAAC,KAAK,CAAC;MACrBM,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,SAAS;MACR,IAAI,CAACiC,MAAM,EAAE;QACXzC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2E,aAAa,GAAG1H,WAAW,CAAC,MAAM;IACtC,IAAI,CAACgD,WAAW,IAAII,OAAO,EAAE;MAC3BsC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEzC,WAAW,EAAEA,WAAW,GAAG,CAAC;QAAEE;MAAQ,CAAC,CAAC;MAC/EkC,SAAS,CAACpC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;IAClC;EACF,CAAC,EAAE,CAACoC,SAAS,EAAEtC,WAAW,EAAEI,OAAO,EAAEF,WAAW,CAAC,CAAC;;EAElD;EACAnD,SAAS,CAAC,MAAM;IACd,MAAM4H,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAACF,SAAS;MACpD,MAAMG,YAAY,GAAGF,QAAQ,CAACC,eAAe,CAACC,YAAY;MAC1D,MAAMC,YAAY,GAAGH,QAAQ,CAACC,eAAe,CAACE,YAAY;;MAE1D;MACA,IAAIJ,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,GAAG,EAAE;QAClDrC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;UACjE3C,WAAW;UACXI,OAAO;UACPF;QACF,CAAC,CAAC;QAEF,IAAI,CAACF,WAAW,IAAII,OAAO,EAAE;UAC3BsE,aAAa,CAAC,CAAC;QACjB;MACF;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAC/C,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;EACjE,CAAC,EAAE,CAACD,aAAa,EAAE1E,WAAW,EAAEI,OAAO,CAAC,CAAC;;EAEzC;EACArD,SAAS,CAAC,MAAM;IACduF,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA;EACA,MAAM3D,WAAW,GAAG1B,OAAO,CAAC,OAAO;IACjCmI,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMzG,iBAAiB,GAAG3B,OAAO,CAAC,OAAO;IACvCqI,IAAI,EAAE,CAAC;IACPvG,WAAW,EAAE,MAAM;IACnB,GAAGJ;EACL,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAElB;EACA,MAAM4G,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAI;MACF,MAAM/C,QAAQ,GAAG,MAAMlF,UAAU,CAACiI,MAAM,CAAC;MACzC,IAAI/C,QAAQ,CAACG,OAAO,EAAE;QACpB/C,QAAQ,CAACD,KAAK,CAACmD,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UACE,GAAGxC,IAAI;UACPvE,OAAO,EAAEgE,QAAQ,CAACK,IAAI,CAAC2C,QAAQ;UAC/B3B,KAAK,EAAErB,QAAQ,CAACK,IAAI,CAACiB;QACvB,CAAC,GACDf,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnF,KAAK,CAAC4G,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C5G,KAAK,CAAC4G,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMiB,gBAAgB,GAAG1I,WAAW,CAAC,OAAOwI,MAAM,EAAEjD,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAC/E,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE;QACdtB,kBAAkB,CAACoD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACmB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC3D,CAAC,MAAM;QACLjE,sBAAsB,CAAC8C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACmB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC/D;MAEA,MAAM/C,QAAQ,GAAG,MAAMhF,eAAe,CAAC+H,MAAM,EAAEjD,IAAI,EAAE,EAAE,CAAC;MAExDG,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEF,QAAQ,CAAC;MAE5E,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAM+C,WAAW,GAAGlD,QAAQ,CAACK,IAAI,CAACkB,QAAQ,CAACjB,GAAG,CAAC6C,OAAO,KAAK;UACzDvD,EAAE,EAAEuD,OAAO,CAACvD,EAAE;UACdY,IAAI,EAAE2C,OAAO,CAACzC,SAAS;UACvBC,MAAM,EAAEwC,OAAO,CAACvC,WAAW,IAAIjG,cAAc;UAC7CyI,IAAI,EAAED,OAAO,CAACA,OAAO;UACrBE,SAAS,EAAE,IAAIC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAC9DjE,OAAO,EAAE4D,OAAO,CAAC5D,OAAO,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,IAAIQ,MAAM,EAAE;UACVzB,eAAe,CAACsD,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACmB,MAAM,GAAG,CAAC,IAAInB,IAAI,CAACmB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAGG,WAAW;UACpD,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL5E,eAAe,CAACsD,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACmB,MAAM,GAAGG;UACZ,CAAC,CAAC,CAAC;QACL;QAEAxE,eAAe,CAACkD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACmB,MAAM,GAAGjD;QAAK,CAAC,CAAC,CAAC;QACtDlB,kBAAkB,CAACgD,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAG/C,QAAQ,CAACK,IAAI,CAACwB,UAAU,CAACC;QACrC,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL1G,KAAK,CAAC4G,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C5G,KAAK,CAAC4G,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRxD,kBAAkB,CAACoD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;MAC1DjE,sBAAsB,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,aAAa,GAAIV,MAAM,IAAK;IAChC,MAAMW,SAAS,GAAG,CAACzF,YAAY,CAAC8E,MAAM,CAAC;IACvC7E,eAAe,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,MAAM,GAAG,CAACnB,IAAI,CAACmB,MAAM;IAAE,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIW,SAAS,IAAI,CAACrF,YAAY,CAAC0E,MAAM,CAAC,EAAE;MACtCE,gBAAgB,CAACF,MAAM,EAAE,CAAC,CAAC;IAC7B;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAIZ,MAAM,IAAK;IACnC,MAAMtF,WAAW,GAAGgB,YAAY,CAACsE,MAAM,CAAC,IAAI,CAAC;IAC7CE,gBAAgB,CAACF,MAAM,EAAEtF,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;EACjD,CAAC;;EAED;EACA,MAAMmG,oBAAoB,GAAGA,CAACb,MAAM,EAAEc,CAAC,KAAK;IAC1C,MAAM;MAAE1B,SAAS;MAAEG,YAAY;MAAEC;IAAa,CAAC,GAAGsB,CAAC,CAACC,MAAM;;IAE1D;IACA,IAAI3B,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,EAAE,EAAE;MACjD,MAAM3E,OAAO,GAAGgB,eAAe,CAACoE,MAAM,CAAC;MACvC,MAAMgB,SAAS,GAAGlF,mBAAmB,CAACkE,MAAM,CAAC;MAE7C,IAAIpF,OAAO,IAAI,CAACoG,SAAS,EAAE;QACzB9D,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;UAAE6C;QAAO,CAAC,CAAC;QAC1EY,gBAAgB,CAACZ,MAAM,CAAC;MAC1B;IACF;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAOjB,MAAM,IAAK;IAC5C,MAAMkB,WAAW,GAAGlG,UAAU,CAACgF,MAAM,CAAC;IACtC,IAAI,CAACkB,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;MACvC9I,KAAK,CAAC4G,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAIiC,WAAW,CAACE,MAAM,GAAG,GAAG,EAAE;MAC5B/I,KAAK,CAAC4G,KAAK,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMjF,UAAU,CAACgI,MAAM,EAAEkB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7D,IAAIlE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACA/C,QAAQ,CAACD,KAAK,CAACmD,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEiB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAG;QAAE,CAAC,GAClDjB,IACN,CAAC,CAAC;;QAEF;QACA,MAAM6D,aAAa,GAAG;UACpBxE,EAAE,EAAEI,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACvD,EAAE;UAC5BY,IAAI,EAAER,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACzC,SAAS;UACrCC,MAAM,EAAEX,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACvC,WAAW,IAAIjG,cAAc;UAC3DyI,IAAI,EAAEpD,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACA,OAAO;UACnCE,SAAS,EAAE,UAAU;UACrB9D,OAAO,EAAEA,OAAO,CAAC;QACnB,CAAC;QAEDjB,eAAe,CAACsD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAG,CAACqB,aAAa,EAAE,IAAIxC,IAAI,CAACmB,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH/E,aAAa,CAAC4D,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACmB,MAAM,GAAG;QAAG,CAAC,CAAC,CAAC;QAClD3H,KAAK,CAAC+E,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,MAAM;QACL/E,KAAK,CAAC4G,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C5G,KAAK,CAAC4G,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMqC,iBAAiB,GAAG,MAAAA,CAAOtB,MAAM,EAAEuB,SAAS,KAAK;IACrD,MAAMC,QAAQ,GAAGtF,eAAe,CAACqF,SAAS,CAAC;IAC3C,IAAI,CAACC,QAAQ,IAAI,CAACA,QAAQ,CAACL,IAAI,CAAC,CAAC,EAAE;MACjC9I,KAAK,CAAC4G,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAIuC,QAAQ,CAACJ,MAAM,GAAG,GAAG,EAAE;MACzB/I,KAAK,CAAC4G,KAAK,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAM/E,WAAW,CAACqJ,SAAS,EAAEC,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC;MAC9D,IAAIlE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACA7B,eAAe,CAACsD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAGnB,IAAI,CAACmB,MAAM,CAAC,CAACzC,GAAG,CAAC6C,OAAO,IAChCA,OAAO,CAACvD,EAAE,KAAK0E,SAAS,GACpB;YAAE,GAAGnB,OAAO;YAAEC,IAAI,EAAEmB,QAAQ,CAACL,IAAI,CAAC;UAAE,CAAC,GACrCf,OACN;QACF,CAAC,CAAC,CAAC;QAEHnE,iBAAiB,CAAC4C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAAC0C,SAAS,GAAG;QAAM,CAAC,CAAC,CAAC;QAC5DpF,kBAAkB,CAAC0C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAAC0C,SAAS,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1DlJ,KAAK,CAAC+E,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACL/E,KAAK,CAAC4G,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C5G,KAAK,CAAC4G,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMwC,mBAAmB,GAAG,MAAAA,CAAOzB,MAAM,EAAEuB,SAAS,KAAK;IACvD,IAAI,CAAC9B,MAAM,CAACiC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMzE,QAAQ,GAAG,MAAM9E,aAAa,CAACoJ,SAAS,CAAC;MAC/C,IAAItE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACA/C,QAAQ,CAACD,KAAK,CAACmD,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEiB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAG;QAAE,CAAC,GAClDjB,IACN,CAAC,CAAC;;QAEF;QACAjC,eAAe,CAACsD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAGnB,IAAI,CAACmB,MAAM,CAAC,CAAC2B,MAAM,CAACvB,OAAO,IAAIA,OAAO,CAACvD,EAAE,KAAK0E,SAAS;QACnE,CAAC,CAAC,CAAC;QAEHlJ,KAAK,CAAC+E,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACL/E,KAAK,CAAC4G,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C5G,KAAK,CAAC4G,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAM2C,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C3E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0E,OAAO,CAAC;;IAErD;IACAxG,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACAuD,UAAU,CAAC,YAAY;MACrB,IAAI;QACF;QACA,MAAM9B,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;QACzBzB,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAC,CAAC,OAAO4D,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE5D,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;EAID,MAAMyG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3H,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM4H,WAAW,GAAG,MAAOvE,IAAI,IAAK;IAClC,IAAI;MACF;MACA,MAAMP,QAAQ,GAAG,MAAM7E,oBAAoB,CAACoF,IAAI,CAACX,EAAE,CAAC;MAEpD,IAAII,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAM4E,QAAQ,GAAG/E,QAAQ,CAACK,IAAI,CAAC0E,QAAQ;;QAEvC;QACA,MAAMC,SAAS,GAAG;UAChBC,KAAK,EAAE,GAAG1E,IAAI,CAACC,IAAI,CAACC,IAAI,SAAS;UACjC2C,IAAI,EAAE7C,IAAI,CAACM,OAAO,IAAI,sBAAsB;UAC5CM,GAAG,EAAE4D;QACP,CAAC;;QAED;QACA,IAAIG,SAAS,CAACC,KAAK,EAAE;UACnB,MAAMD,SAAS,CAACC,KAAK,CAACH,SAAS,CAAC;UAChC/E,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACpC,CAAC,MAAM;UACL;UACA;UACA,MAAMgF,SAAS,CAACE,SAAS,CAACC,SAAS,CAACN,QAAQ,CAAC;UAC7C3J,KAAK,CAAC+E,OAAO,CAAC,gCAAgC,CAAC;QACjD;MACF,CAAC,MAAM;QACL/E,KAAK,CAAC4G,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAIA,KAAK,CAACvB,IAAI,KAAK,YAAY,EAAE;QAC/BrF,KAAK,CAAC4G,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;EACF,CAAC;;EAED;EACA,MAAMsD,WAAW,GAAIvE,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMwE,UAAU,GAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ,CAAC;IAExD,IAAI1E,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B,oBAAO3F,OAAA;QAAKoK,GAAG,EAAE3E,KAAK,CAACI,GAAI;QAAC5E,SAAS,EAAC,mBAAmB;QAACoJ,GAAG,EAAC,YAAY;QAACnJ,KAAK,EAAE;UAAC,GAAG+I,UAAU;UAAEK,SAAS,EAAE;QAAS;MAAE;QAAAjJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7H,CAAC,MAAM,IAAIiE,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MACjC,oBACE3F,OAAA;QAAOiB,SAAS,EAAC,mBAAmB;QAACsJ,QAAQ;QAACrJ,KAAK,EAAE+I,UAAW;QAAA9I,QAAA,gBAC9DnB,OAAA;UAAQoK,GAAG,EAAE3E,KAAK,CAACI,GAAI;UAACF,IAAI,EAAC;QAAW;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMgJ,iBAAiB,GAAGA,CAACjF,OAAO,EAAEN,IAAI,KAAK;IAC3C,IAAI,CAACM,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAMkF,QAAQ,GAAGxF,IAAI,CAACQ,KAAK,KAAKR,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIV,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,CAAC;;IAE3F;IACA,IAAI,CAAC8E,QAAQ,EAAE;MACb,oBACEzK,OAAA;QAAAmB,QAAA,eACEnB,OAAA;UAAGiB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAEoE;QAAO;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAEV;;IAEA;IACA,MAAMkJ,cAAc,GAAGnF,OAAO,CAACsD,MAAM,GAAG,GAAG;IAC3C,MAAM8B,aAAa,GAAG5G,YAAY,CAACkB,IAAI,CAACX,EAAE,CAAC;IAC3C,MAAMsG,WAAW,GAAGD,aAAa,GAAGpF,OAAO,GAAGA,OAAO,CAACsF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,cAAc,GAAG,KAAK,GAAG,EAAE,CAAC;IAEvG,oBACE1K,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QAAGiB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,GAC1ByJ,WAAW,EACXF,cAAc,iBACb1K,OAAA;UACEiB,SAAS,EAAC,yDAAyD;UACnER,OAAO,EAAEA,CAAA,KAAMuD,eAAe,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACrB,IAAI,CAACX,EAAE,GAAG,CAACqG;UAAc,CAAC,CAAC,CAAE;UAAAxJ,QAAA,EAEhFwJ,aAAa,GAAG,WAAW,GAAG;QAAW;UAAAtJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsJ,mBAAmB,GAAG7L,WAAW,CAAC,CAACwI,MAAM,EAAEsD,KAAK,KAAK;IACzDrI,aAAa,CAAC4D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,MAAM,GAAGsD;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAG/L,WAAW,CAAC,CAACsJ,CAAC,EAAEd,MAAM,KAAK;IACtD,IAAIc,CAAC,CAAC0C,GAAG,KAAK,OAAO,EAAE;MACrBvC,mBAAmB,CAACjB,MAAM,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyD,wBAAwB,GAAGjM,WAAW,CAAEwI,MAAM,IAAK;IACvDiB,mBAAmB,CAACjB,MAAM,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0D,uBAAuB,GAAGlM,WAAW,CAAC,CAAC+J,SAAS,EAAE+B,KAAK,KAAK;IAChEnH,kBAAkB,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC0C,SAAS,GAAG+B;IAAM,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,wBAAwB,GAAGnM,WAAW,CAAC,CAACsJ,CAAC,EAAEd,MAAM,EAAEuB,SAAS,KAAK;IACrE,IAAIT,CAAC,CAAC0C,GAAG,KAAK,OAAO,EAAE;MACrBlC,iBAAiB,CAACtB,MAAM,EAAEuB,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqC,cAAc,GAAIpG,IAAI,IAAK;IAC/B,IAAI,CAACtC,YAAY,CAACsC,IAAI,CAACX,EAAE,CAAC,EAAE,OAAO,IAAI;IAEvC,MAAM2B,QAAQ,GAAGlD,YAAY,CAACkC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE;IAC5C,MAAMmE,SAAS,GAAGxF,eAAe,CAACgC,IAAI,CAACX,EAAE,CAAC;IAC1C,MAAMgH,aAAa,GAAG/H,mBAAmB,CAAC0B,IAAI,CAACX,EAAE,CAAC;IAClD,MAAMjC,OAAO,GAAGgB,eAAe,CAAC4B,IAAI,CAACX,EAAE,CAAC;IAExC,oBACEtE,OAAA;MAAKiB,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCnB,OAAA;QAAIiB,SAAS,EAAC,MAAM;QAAAE,QAAA,GAAC,YAAU,EAAC8D,IAAI,CAACiB,aAAa,IAAI,CAAC,EAAC,GAAC;MAAA;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG9DxB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BnB,OAAA;UAAKoK,GAAG,EAAE/K,cAAe;UAAC4B,SAAS,EAAC,qBAAqB;UAACoJ,GAAG,EAAC,SAAS;UAACnJ,KAAK,EAAE;YAACgJ,KAAK,EAAE,MAAM;YAAEqB,MAAM,EAAE;UAAM;QAAE;UAAAlK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHxB,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BnB,OAAA;YACE2F,IAAI,EAAC,MAAM;YACX1E,SAAS,EAAC,cAAc;YACxBuK,WAAW,EAAC,oBAAoB;YAChCT,KAAK,EAAEtI,UAAU,CAACwC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAG;YACjCmH,QAAQ,EAAGlD,CAAC,IAAKuC,mBAAmB,CAAC7F,IAAI,CAACX,EAAE,EAAEiE,CAAC,CAACC,MAAM,CAACuC,KAAK,CAAE;YAC9DW,SAAS,EAAGnD,CAAC,IAAKyC,oBAAoB,CAACzC,CAAC,EAAEtD,IAAI,CAACX,EAAE,CAAE;YACnDqH,SAAS,EAAE;UAAI;YAAAtK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFxB,OAAA;YAAKiB,SAAS,EAAC,iCAAiC;YAAAE,QAAA,eAC9CnB,OAAA;cAAOiB,SAAS,EAAE,CAACwB,UAAU,CAACwC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE,EAAEuE,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;cAAA1H,QAAA,GACxF,CAACsB,UAAU,CAACwC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE,EAAEuE,MAAM,EAAC,iBACtC;YAAA;cAAAxH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UACEiB,SAAS,EAAC,oCAAoC;UAC9CR,OAAO,EAAEA,CAAA,KAAMyK,wBAAwB,CAACjG,IAAI,CAACX,EAAE,CAAE;UACjDsH,QAAQ,EAAE,CAACnJ,UAAU,CAACwC,IAAI,CAACX,EAAE,CAAC,IAAI,CAAC7B,UAAU,CAACwC,IAAI,CAACX,EAAE,CAAC,CAACsE,IAAI,CAAC,CAAE;UAAAzH,QAAA,eAE9DnB,OAAA,CAACb,IAAI;YAACoB,IAAI,EAAC;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLiH,SAAS,gBACRzI,OAAA;QAAKiB,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BnB,OAAA;UAAKiB,SAAS,EAAC,kCAAkC;UAAC4K,IAAI,EAAC,QAAQ;UAAA1K,QAAA,eAC7DnB,OAAA;YAAMiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNxB,OAAA;UAAGiB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,gBAENxB,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eAEEnB,OAAA;UACEkB,KAAK,EAAE;YAAEiJ,SAAS,EAAE,OAAO;YAAE2B,SAAS,EAAE;UAAO,CAAE;UACjDxH,EAAE,EAAE,sBAAsBW,IAAI,CAACX,EAAE,EAAG;UACpCyH,QAAQ,EAAGxD,CAAC,IAAKD,oBAAoB,CAACrD,IAAI,CAACX,EAAE,EAAEiE,CAAC,CAAE;UAAApH,QAAA,GAGjD8E,QAAQ,CAACjB,GAAG,CAAC6C,OAAO,iBACnB7H,OAAA;YAAsBiB,SAAS,EAAC,aAAa;YAAAE,QAAA,gBAC3CnB,OAAA;cAAKoK,GAAG,EAAEvC,OAAO,CAACxC,MAAO;cAACpE,SAAS,EAAC,qBAAqB;cAACoJ,GAAG,EAAExC,OAAO,CAAC3C,IAAK;cAAChE,KAAK,EAAE;gBAACgJ,KAAK,EAAE,MAAM;gBAAEqB,MAAM,EAAE;cAAM;YAAE;cAAAlK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvHxB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAAE,QAAA,gBAC/CnB,OAAA;gBAAKiB,SAAS,EAAC,kDAAkD;gBAAAE,QAAA,gBAC/DnB,OAAA;kBAAKiB,SAAS,EAAC,SAAS;kBAAAE,QAAA,EAAE0G,OAAO,CAAC3C;gBAAI;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAE5CqG,OAAO,CAAC5D,OAAO,KAAKA,OAAO,iBAC1BjE,OAAA;kBAAKiB,SAAS,EAAC,cAAc;kBAAAE,QAAA,EAC1BsC,cAAc,CAACoE,OAAO,CAACvD,EAAE,CAAC,gBACzBtE,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,wBAAwB;sBAClCR,OAAO,EAAEA,CAAA,KAAMsI,iBAAiB,CAAC9D,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;sBAAAnD,QAAA,eAEtDnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,0BAA0B;sBACpCR,OAAO,EAAEA,CAAA,KAAM;wBACbiD,iBAAiB,CAAC4C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAM,CAAC,CAAC,CAAC;wBAC7DV,kBAAkB,CAAC0C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAG,CAAC,CAAC,CAAC;sBAC7D,CAAE;sBAAAnD,QAAA,eAEFnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA,eACT,CAAC,gBAEHxB,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,gCAAgC;sBAC1CR,OAAO,EAAEA,CAAA,KAAM;wBACbiD,iBAAiB,CAAC4C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAK,CAAC,CAAC,CAAC;wBAC5DV,kBAAkB,CAAC0C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAGuD,OAAO,CAACC;wBAAK,CAAC,CAAC,CAAC;sBACvE,CAAE;sBACF6B,KAAK,EAAC,cAAc;sBAAAxI,QAAA,eAEpBnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,+BAA+B;sBACzCR,OAAO,EAAEA,CAAA,KAAMyI,mBAAmB,CAACjE,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;sBACxDqF,KAAK,EAAC,gBAAgB;sBAAAxI,QAAA,eAEtBnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAELiC,cAAc,CAACoE,OAAO,CAACvD,EAAE,CAAC,gBACzBtE,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,gBACnBnB,OAAA;kBACE2F,IAAI,EAAC,MAAM;kBACX1E,SAAS,EAAC,8BAA8B;kBACxC8J,KAAK,EAAEpH,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAG;kBACzCmH,QAAQ,EAAGlD,CAAC,IAAK4C,uBAAuB,CAACtD,OAAO,CAACvD,EAAE,EAAEiE,CAAC,CAACC,MAAM,CAACuC,KAAK,CAAE;kBACrEW,SAAS,EAAGnD,CAAC,IAAK6C,wBAAwB,CAAC7C,CAAC,EAAEtD,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;kBACnEqH,SAAS,EAAE,GAAI;kBACfK,SAAS;gBAAA;kBAAA3K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFxB,OAAA;kBAAKiB,SAAS,EAAC,iCAAiC;kBAAAE,QAAA,eAC9CnB,OAAA;oBAAOiB,SAAS,EAAE,CAAC0C,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAE,EAAEuE,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;oBAAA1H,QAAA,GAChG,CAACwC,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAE,EAAEuE,MAAM,EAAC,iBAC9C;kBAAA;oBAAAxH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENxB,OAAA;gBAAAmB,QAAA,EAAM0G,OAAO,CAACC;cAAI;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACzB,eAEDxB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAE0G,OAAO,CAACE;cAAS;gBAAA1G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GAzEEqG,OAAO,CAACvD,EAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0Ef,CACN,CAAC,EAGD8J,aAAa,iBACZtL,OAAA;YAAKiB,SAAS,EAAC,uBAAuB;YAAAE,QAAA,gBACpCnB,OAAA;cAAKiB,SAAS,EAAC,6CAA6C;cAAC4K,IAAI,EAAC,QAAQ;cAAA1K,QAAA,eACxEnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNxB,OAAA;cAAMiB,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,gBACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAID,oBACExB,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAE,QAAA,gBAC7BnB,OAAA;MAAAmB,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACRxB,OAAA;MAAKiB,SAAS,EAAC,4BAA4B;MAAAE,QAAA,eACzCnB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAE,QAAA,gBAEvBnB,OAAA;UAAKiB,SAAS,EAAC,wDAAwD;UAAAE,QAAA,gBACrEnB,OAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXxB,OAAA;YACEiB,SAAS,EAAC,2BAA2B;YACrCR,OAAO,EAAE8I,iBAAkB;YAC3BrI,KAAK,EAAE;cACL+K,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAE;YACFC,YAAY,EAAG9D,CAAC,IAAKA,CAAC,CAAC+D,aAAa,CAACpL,KAAK,CAACmG,eAAe,GAAG,SAAU;YACvEkF,YAAY,EAAGhE,CAAC,IAAKA,CAAC,CAAC+D,aAAa,CAACpL,KAAK,CAACmG,eAAe,GAAG,aAAc;YAAAlG,QAAA,gBAE3EnB,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5BnB,OAAA;gBAAIiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,EAAE,CAAA0C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,IAAI,KAAI;cAAM;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDxB,OAAA;gBAAOiB,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNxB,OAAA;cACEoK,GAAG,EAAE,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2I,eAAe,KAAInN,cAAe;cACpD4B,SAAS,EAAC,gBAAgB;cAC1BoJ,GAAG,EAAE,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,IAAI,KAAI,cAAe;cACzCjE,KAAK,EAAE;gBAACgJ,KAAK,EAAE,MAAM;gBAAEqB,MAAM,EAAE;cAAM;YAAE;cAAAlK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA,CAACV,QAAQ;UAACmN,YAAY,EAAEpD,gBAAiB;UAACxF,WAAW,EAAEA;QAAY;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGrEqB,cAAc,iBACb7C,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAACC,KAAK,EAAE;YAChCwL,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE,oBAAoB;YAC5BtF,eAAe,EAAE;UACnB,CAAE;UAAAlG,QAAA,eACAnB,OAAA;YAAKiB,SAAS,EAAC,4BAA4B;YAAAE,QAAA,gBACzCnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAC4K,IAAI,EAAC,QAAQ;cAAA1K,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNxB,OAAA;cAAIiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxB,OAAA;cAAGiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAO,OAAO,gBACN/B,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAAC4K,IAAI,EAAC,QAAQ;YAAA1K,QAAA,eAC3CnB,OAAA;cAAMiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNxB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,GACJK,KAAK,CAACgH,MAAM,KAAK,CAAC,gBACpB7I,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA,CAACb,IAAI;YAACoB,IAAI,EAAC,kBAAkB;YAACW,KAAK,EAAE;cAAEE,QAAQ,EAAE,MAAM;cAAEwL,KAAK,EAAE;YAAU;UAAE;YAAAvL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ExB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,gBAENxB,OAAA,CAAAE,SAAA;UAAAiB,QAAA,GAEGU,KAAK,CAACmD,GAAG,CAAC,CAACC,IAAI,EAAE4H,KAAK,kBACrB7M,OAAA;YAEEiB,SAAS,EAAC,WAAW;YACrBC,KAAK,EAAE;cACLwL,SAAS,EAAEG,KAAK,KAAK,CAAC,IAAI,CAAChK,cAAc,GAAG,2BAA2B,GAAG,MAAM;cAChFiK,SAAS,EAAED,KAAK,KAAK,CAAC,IAAI,CAAChK,cAAc,GAAG,eAAe,GAAG;YAChE,CAAE;YAAA1B,QAAA,eAEFnB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAE,QAAA,gBAExBnB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA;kBAAKoK,GAAG,EAAEnF,IAAI,CAACC,IAAI,CAACG,MAAO;kBAACpE,SAAS,EAAC,qBAAqB;kBAACoJ,GAAG,EAAEpF,IAAI,CAACC,IAAI,CAACC,IAAK;kBAACjE,KAAK,EAAE;oBAACgJ,KAAK,EAAE,MAAM;oBAAEqB,MAAM,EAAE;kBAAM;gBAAE;kBAAAlK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3HxB,OAAA;kBAAKiB,SAAS,EAAC,aAAa;kBAAAE,QAAA,gBAC1BnB,OAAA;oBAAIiB,SAAS,EAAC,MAAM;oBAAAE,QAAA,EAAE8D,IAAI,CAACC,IAAI,CAACC;kBAAI;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CxB,OAAA;oBAAOiB,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE,IAAI6G,IAAI,CAAC/C,IAAI,CAACmB,UAAU,CAAC,CAAC8B,kBAAkB,CAAC;kBAAC;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,GAClBqJ,iBAAiB,CAACvF,IAAI,CAACM,OAAO,EAAEN,IAAI,CAAC,EACrC+E,WAAW,CAAC/E,IAAI,CAACQ,KAAK,CAAC;cAAA;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAE0E,IAAI,CAACvE,OAAO,GAAG,WAAW,GAAG,mBAAoB;kBACvDF,KAAK,EAAEyE,IAAI,CAACc,KAAM;kBAClBtF,OAAO,EAAEA,CAAA,KAAM+G,UAAU,CAACvC,IAAI,CAACX,EAAE,CAAE;kBACnC5D,OAAO,EAAEuE,IAAI,CAACvE,OAAQ;kBACtBE,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEyE,IAAI,CAACiB,aAAa,IAAI,CAAE;kBAC/BzF,OAAO,EAAEA,CAAA,KAAM0H,aAAa,CAAClD,IAAI,CAACX,EAAE,CAAE;kBACtC1D,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,2BAA2B;kBAChCE,OAAO,EAAEA,CAAA,KAAM+I,WAAW,CAACvE,IAAI,CAAE;kBACjCtE,MAAM,EAAE,IAAK;kBACbC,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL6J,cAAc,CAACpG,IAAI,CAAC;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC,GAnDDyD,IAAI,CAACX,EAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDT,CACN,CAAC,EAGDe,oBAAoB,iBACnBvC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAACC,KAAK,EAAE;cACvCwL,SAAS,EAAE;YACb,CAAE;YAAAvL,QAAA,gBACAnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAC4K,IAAI,EAAC,QAAQ;cAAA1K,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNxB,OAAA;cAAGiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN,EAEA,CAACe,oBAAoB,IAAI,CAACN,WAAW,IAAII,OAAO,iBAC/CrC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cACEiB,SAAS,EAAC,yBAAyB;cACnCR,OAAO,EAAEkG,aAAc;cAAAxF,QAAA,gBAEvBnB,OAAA,CAACb,IAAI;gBAACoB,IAAI,EAAC,kBAAkB;gBAACU,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEA,CAACa,OAAO,IAAIR,KAAK,CAACgH,MAAM,GAAG,CAAC,iBAC3B7I,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cAAGiB,SAAS,EAAC,YAAY;cAAAE,QAAA,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACN;QAAA,eAED,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,GAAA,CAl1BID,IAAI;EAAA,QACStC,WAAW;AAAA;AAAA2N,GAAA,GADxBrL,IAAI;AAo1BV,eAAeA,IAAI;AAAC,IAAApB,EAAA,EAAAmB,GAAA,EAAAsL,GAAA;AAAAC,YAAA,CAAA1M,EAAA;AAAA0M,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}