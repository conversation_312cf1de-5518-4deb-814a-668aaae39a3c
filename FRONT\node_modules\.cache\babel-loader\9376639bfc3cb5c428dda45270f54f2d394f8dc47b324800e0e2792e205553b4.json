{"ast": null, "code": "import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';\nconst endpoint = \"/feed\";\n\n// Create a new post\nexport const createPost = async postData => {\n  const formData = new FormData();\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\n};\n\n// Edit an existing post\nexport const editPost = async (postId, postData) => {\n  const formData = new FormData();\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n  return UPLOAD_FILE(endpoint + `/edit_post/${postId}`, formData, 'PUT');\n};\n\n// Delete a post\nexport const deletePost = async postId => {\n  return DELETE(endpoint + `/delete_post/${postId}`);\n};\n\n// Get all feeds with pagination\nexport const getAllFeeds = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", {\n    page,\n    limit\n  });\n};\n\n// Toggle like on a post\nexport const toggleLike = async postId => {\n  return POST(endpoint + `/toggle_like/${postId}`, {});\n};\n\n// Add a comment to a post\nexport const addComment = async (postId, comment) => {\n  return POST(endpoint + `/add_comment/${postId}`, {\n    comment\n  });\n};\n\n// Edit a comment\nexport const editComment = async (commentId, comment) => {\n  return PUT(endpoint + `/edit_comment/${commentId}`, {\n    comment\n  });\n};\n\n// Delete a comment\nexport const deleteComment = async commentId => {\n  return DELETE(endpoint + `/delete_comment/${commentId}`);\n};\n\n// Get comments for a specific post\nexport const getPostComments = async (postId, page = 1, limit = 10) => {\n  return GET(endpoint + `/get_comments/${postId}?page=${page}&limit=${limit}`);\n};\n\n// Get user's own posts (for MyFeed component)\nexport const getMyPosts = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", {\n    page,\n    limit,\n    user_only: true\n  });\n};", "map": {"version": 3, "names": ["GET", "POST", "UPLOAD_FILE", "PUT", "DELETE", "endpoint", "createPost", "postData", "formData", "FormData", "description", "append", "media", "file", "editPost", "postId", "deletePost", "getAllFeeds", "page", "limit", "toggleLike", "addComment", "comment", "editComment", "commentId", "deleteComment", "getPostComments", "getMyPosts", "user_only"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/feedServices.js"], "sourcesContent": ["import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';\r\n\r\nconst endpoint = \"/feed\";\r\n\r\n// Create a new post\r\nexport const createPost = async (postData) => {\r\n  const formData = new FormData();\r\n\r\n  if (postData.description) {\r\n    formData.append('description', postData.description);\r\n  }\r\n\r\n  if (postData.media && postData.media.file) {\r\n    formData.append('media', postData.media.file);\r\n  }\r\n\r\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\r\n};\r\n\r\n// Edit an existing post\r\nexport const editPost = async (postId, postData) => {\r\n  const formData = new FormData();\r\n\r\n  if (postData.description) {\r\n    formData.append('description', postData.description);\r\n  }\r\n\r\n  if (postData.media && postData.media.file) {\r\n    formData.append('media', postData.media.file);\r\n  }\r\n\r\n  return UPLOAD_FILE(endpoint + `/edit_post/${postId}`, formData, 'PUT');\r\n};\r\n\r\n// Delete a post\r\nexport const deletePost = async (postId) => {\r\n  return DELETE(endpoint + `/delete_post/${postId}`);\r\n};\r\n\r\n// Get all feeds with pagination\r\nexport const getAllFeeds = async (page = 1, limit = 5) => {\r\n  return POST(endpoint + \"/get_all_feeds\", { page, limit });\r\n};\r\n\r\n// Toggle like on a post\r\nexport const toggleLike = async (postId) => {\r\n  return POST(endpoint + `/toggle_like/${postId}`, {});\r\n};\r\n\r\n// Add a comment to a post\r\nexport const addComment = async (postId, comment) => {\r\n  return POST(endpoint + `/add_comment/${postId}`, { comment });\r\n};\r\n\r\n// Edit a comment\r\nexport const editComment = async (commentId, comment) => {\r\n  return PUT(endpoint + `/edit_comment/${commentId}`, { comment });\r\n};\r\n\r\n// Delete a comment\r\nexport const deleteComment = async (commentId) => {\r\n  return DELETE(endpoint + `/delete_comment/${commentId}`);\r\n};\r\n\r\n// Get comments for a specific post\r\nexport const getPostComments = async (postId, page = 1, limit = 10) => {\r\n  return GET(endpoint + `/get_comments/${postId}?page=${page}&limit=${limit}`);\r\n};\r\n\r\n// Get user's own posts (for MyFeed component)\r\nexport const getMyPosts = async (page = 1, limit = 5) => {\r\n  return POST(endpoint + \"/get_all_feeds\", { page, limit, user_only: true });\r\n};"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAErE,MAAMC,QAAQ,GAAG,OAAO;;AAExB;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/B,IAAIF,QAAQ,CAACG,WAAW,EAAE;IACxBF,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAEJ,QAAQ,CAACG,WAAW,CAAC;EACtD;EAEA,IAAIH,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACC,IAAI,EAAE;IACzCL,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEJ,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC;EAC/C;EAEA,OAAOX,WAAW,CAACG,QAAQ,GAAG,cAAc,EAAEG,QAAQ,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMM,QAAQ,GAAG,MAAAA,CAAOC,MAAM,EAAER,QAAQ,KAAK;EAClD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/B,IAAIF,QAAQ,CAACG,WAAW,EAAE;IACxBF,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAEJ,QAAQ,CAACG,WAAW,CAAC;EACtD;EAEA,IAAIH,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACC,IAAI,EAAE;IACzCL,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEJ,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC;EAC/C;EAEA,OAAOX,WAAW,CAACG,QAAQ,GAAG,cAAcU,MAAM,EAAE,EAAEP,QAAQ,EAAE,KAAK,CAAC;AACxE,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAG,MAAOD,MAAM,IAAK;EAC1C,OAAOX,MAAM,CAACC,QAAQ,GAAG,gBAAgBU,MAAM,EAAE,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAME,WAAW,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,KAAK;EACxD,OAAOlB,IAAI,CAACI,QAAQ,GAAG,gBAAgB,EAAE;IAAEa,IAAI;IAAEC;EAAM,CAAC,CAAC;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOL,MAAM,IAAK;EAC1C,OAAOd,IAAI,CAACI,QAAQ,GAAG,gBAAgBU,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;AACtD,CAAC;;AAED;AACA,OAAO,MAAMM,UAAU,GAAG,MAAAA,CAAON,MAAM,EAAEO,OAAO,KAAK;EACnD,OAAOrB,IAAI,CAACI,QAAQ,GAAG,gBAAgBU,MAAM,EAAE,EAAE;IAAEO;EAAQ,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEF,OAAO,KAAK;EACvD,OAAOnB,GAAG,CAACE,QAAQ,GAAG,iBAAiBmB,SAAS,EAAE,EAAE;IAAEF;EAAQ,CAAC,CAAC;AAClE,CAAC;;AAED;AACA,OAAO,MAAMG,aAAa,GAAG,MAAOD,SAAS,IAAK;EAChD,OAAOpB,MAAM,CAACC,QAAQ,GAAG,mBAAmBmB,SAAS,EAAE,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAG,MAAAA,CAAOX,MAAM,EAAEG,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;EACrE,OAAOnB,GAAG,CAACK,QAAQ,GAAG,iBAAiBU,MAAM,SAASG,IAAI,UAAUC,KAAK,EAAE,CAAC;AAC9E,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAG,MAAAA,CAAOT,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,KAAK;EACvD,OAAOlB,IAAI,CAACI,QAAQ,GAAG,gBAAgB,EAAE;IAAEa,IAAI;IAAEC,KAAK;IAAES,SAAS,EAAE;EAAK,CAAC,CAAC;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}