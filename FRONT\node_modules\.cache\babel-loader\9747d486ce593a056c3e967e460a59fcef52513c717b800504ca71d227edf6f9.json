{"ast": null, "code": "import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';\nconst endpoint = \"/feed\";\n\n// Create a new post\nexport const createPost = async postData => {\n  const formData = new FormData();\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n  formData.append('domain', window.location.origin);\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\n};\n\n// Edit an existing post\nexport const editPost = async (postId, postData) => {\n  const formData = new FormData();\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n  formData.append('domain', window.location.origin);\n  return UPLOAD_FILE(endpoint + `/edit_post/${postId}`, formData, 'PUT');\n};\n\n// Delete a post\nexport const deletePost = async postId => {\n  return DELETE(endpoint + `/delete_post/${postId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Get all feeds with pagination\nexport const getAllFeeds = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", {\n    page,\n    limit,\n    domain: window.location.origin\n  });\n};\n\n// Toggle like on a post\nexport const toggleLike = async postId => {\n  return POST(endpoint + `/toggle_like/${postId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Add a comment to a post\nexport const addComment = async (postId, comment) => {\n  return POST(endpoint + `/add_comment/${postId}`, {\n    comment,\n    domain: window.location.origin\n  });\n};\n\n// Edit a comment\nexport const editComment = async (commentId, comment) => {\n  return PUT(endpoint + `/edit_comment/${commentId}`, {\n    comment,\n    domain: window.location.origin\n  });\n};\n\n// Delete a comment\nexport const deleteComment = async commentId => {\n  return DELETE(endpoint + `/delete_comment/${commentId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Get comments for a specific post\nexport const getPostComments = async (postId, page = 1, limit = 10) => {\n  return GET(endpoint + `/get_comments/${postId}?page=${page}&limit=${limit}`);\n};\n\n// Get user's own posts (for MyFeed component)\nexport const getMyPosts = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", {\n    page,\n    limit,\n    user_only: true,\n    domain: window.location.origin\n  });\n};\n\n// Generate shareable URL for a post\nexport const generatePostShareUrl = async postId => {\n  return POST(endpoint + \"/generate_share_url\", {\n    post_id: postId,\n    domain: window.location.origin\n  });\n};\n\n// Get public post details (no authentication required)\nexport const getPublicPostDetails = async data => {\n  return POST(\"/feed/get_public_post_details\", data);\n};", "map": {"version": 3, "names": ["GET", "POST", "UPLOAD_FILE", "PUT", "DELETE", "endpoint", "createPost", "postData", "formData", "FormData", "description", "append", "media", "file", "window", "location", "origin", "editPost", "postId", "deletePost", "domain", "getAllFeeds", "page", "limit", "toggleLike", "addComment", "comment", "editComment", "commentId", "deleteComment", "getPostComments", "getMyPosts", "user_only", "generatePostShareUrl", "post_id", "getPublicPostDetails", "data"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/feedServices.js"], "sourcesContent": ["import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';\n\nconst endpoint = \"/feed\";\n\n// Create a new post\nexport const createPost = async (postData) => {\n  const formData = new FormData();\n\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n\n  formData.append('domain', window.location.origin);\n\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\n};\n\n// Edit an existing post\nexport const editPost = async (postId, postData) => {\n  const formData = new FormData();\n\n  if (postData.description) {\n    formData.append('description', postData.description);\n  }\n\n  if (postData.media && postData.media.file) {\n    formData.append('media', postData.media.file);\n  }\n\n  formData.append('domain', window.location.origin);\n\n  return UPLOAD_FILE(endpoint + `/edit_post/${postId}`, formData, 'PUT');\n};\n\n// Delete a post\nexport const deletePost = async (postId) => {\n  return DELETE(endpoint + `/delete_post/${postId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Get all feeds with pagination\nexport const getAllFeeds = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", { \n    page, \n    limit,\n    domain: window.location.origin \n  });\n};\n\n// Toggle like on a post\nexport const toggleLike = async (postId) => {\n  return POST(endpoint + `/toggle_like/${postId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Add a comment to a post\nexport const addComment = async (postId, comment) => {\n  return POST(endpoint + `/add_comment/${postId}`, { \n    comment,\n    domain: window.location.origin \n  });\n};\n\n// Edit a comment\nexport const editComment = async (commentId, comment) => {\n  return PUT(endpoint + `/edit_comment/${commentId}`, { \n    comment,\n    domain: window.location.origin \n  });\n};\n\n// Delete a comment\nexport const deleteComment = async (commentId) => {\n  return DELETE(endpoint + `/delete_comment/${commentId}`, {\n    domain: window.location.origin\n  });\n};\n\n// Get comments for a specific post\nexport const getPostComments = async (postId, page = 1, limit = 10) => {\n  return GET(endpoint + `/get_comments/${postId}?page=${page}&limit=${limit}`);\n};\n\n// Get user's own posts (for MyFeed component)\nexport const getMyPosts = async (page = 1, limit = 5) => {\n  return POST(endpoint + \"/get_all_feeds\", { \n    page, \n    limit, \n    user_only: true,\n    domain: window.location.origin \n  });\n};\n\n// Generate shareable URL for a post\nexport const generatePostShareUrl = async (postId) => {\n  return POST(endpoint + \"/generate_share_url\", {\n    post_id: postId,\n    domain: window.location.origin\n  });\n};\n\n// Get public post details (no authentication required)\nexport const getPublicPostDetails = async (data) => {\n  return POST(\"/feed/get_public_post_details\", data);\n};"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAErE,MAAMC,QAAQ,GAAG,OAAO;;AAExB;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/B,IAAIF,QAAQ,CAACG,WAAW,EAAE;IACxBF,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAEJ,QAAQ,CAACG,WAAW,CAAC;EACtD;EAEA,IAAIH,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACC,IAAI,EAAE;IACzCL,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEJ,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC;EAC/C;EAEAL,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAEjD,OAAOd,WAAW,CAACG,QAAQ,GAAG,cAAc,EAAEG,QAAQ,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMS,QAAQ,GAAG,MAAAA,CAAOC,MAAM,EAAEX,QAAQ,KAAK;EAClD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/B,IAAIF,QAAQ,CAACG,WAAW,EAAE;IACxBF,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAEJ,QAAQ,CAACG,WAAW,CAAC;EACtD;EAEA,IAAIH,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACC,IAAI,EAAE;IACzCL,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEJ,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC;EAC/C;EAEAL,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAEjD,OAAOd,WAAW,CAACG,QAAQ,GAAG,cAAca,MAAM,EAAE,EAAEV,QAAQ,EAAE,KAAK,CAAC;AACxE,CAAC;;AAED;AACA,OAAO,MAAMW,UAAU,GAAG,MAAOD,MAAM,IAAK;EAC1C,OAAOd,MAAM,CAACC,QAAQ,GAAG,gBAAgBa,MAAM,EAAE,EAAE;IACjDE,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMK,WAAW,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,KAAK;EACxD,OAAOtB,IAAI,CAACI,QAAQ,GAAG,gBAAgB,EAAE;IACvCiB,IAAI;IACJC,KAAK;IACLH,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAG,MAAON,MAAM,IAAK;EAC1C,OAAOjB,IAAI,CAACI,QAAQ,GAAG,gBAAgBa,MAAM,EAAE,EAAE;IAC/CE,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMS,UAAU,GAAG,MAAAA,CAAOP,MAAM,EAAEQ,OAAO,KAAK;EACnD,OAAOzB,IAAI,CAACI,QAAQ,GAAG,gBAAgBa,MAAM,EAAE,EAAE;IAC/CQ,OAAO;IACPN,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMW,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEF,OAAO,KAAK;EACvD,OAAOvB,GAAG,CAACE,QAAQ,GAAG,iBAAiBuB,SAAS,EAAE,EAAE;IAClDF,OAAO;IACPN,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMa,aAAa,GAAG,MAAOD,SAAS,IAAK;EAChD,OAAOxB,MAAM,CAACC,QAAQ,GAAG,mBAAmBuB,SAAS,EAAE,EAAE;IACvDR,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMc,eAAe,GAAG,MAAAA,CAAOZ,MAAM,EAAEI,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;EACrE,OAAOvB,GAAG,CAACK,QAAQ,GAAG,iBAAiBa,MAAM,SAASI,IAAI,UAAUC,KAAK,EAAE,CAAC;AAC9E,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAG,MAAAA,CAAOT,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,KAAK;EACvD,OAAOtB,IAAI,CAACI,QAAQ,GAAG,gBAAgB,EAAE;IACvCiB,IAAI;IACJC,KAAK;IACLS,SAAS,EAAE,IAAI;IACfZ,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMiB,oBAAoB,GAAG,MAAOf,MAAM,IAAK;EACpD,OAAOjB,IAAI,CAACI,QAAQ,GAAG,qBAAqB,EAAE;IAC5C6B,OAAO,EAAEhB,MAAM;IACfE,MAAM,EAAEN,MAAM,CAACC,QAAQ,CAACC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMmB,oBAAoB,GAAG,MAAOC,IAAI,IAAK;EAClD,OAAOnC,IAAI,CAAC,+BAA+B,EAAEmC,IAAI,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}