{"ast": null, "code": "import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';", "map": {"version": 3, "names": ["GET", "POST", "UPLOAD_FILE", "PUT", "DELETE"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/feedRoutes.js"], "sourcesContent": ["import { GET, POST, UPLOAD_FILE , PUT, DELETE } from './apiController';"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAGC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}