import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import DefaultProfile from '../../assets/images/profile/default-profile.png';
import { decodeData } from '../../utils/encodeAndEncode';
import { getPublicPostDetails } from '../../services/feedServices';

const PublicPostDetails = () => {
  const navigate = useNavigate();
  const { encodedId } = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showFullText, setShowFullText] = useState(false);

  // Decode the post ID from URL
  const decoded = decodeData(encodedId);
  const postId = decoded?.id;

  useEffect(() => {
    if (postId) {
      fetchPostDetails();
    } else {
      setError('Invalid post URL');
      setLoading(false);
    }
  }, [postId]);

  const fetchPostDetails = async () => {
    try {
      setLoading(true);
      const response = await getPublicPostDetails({
        post_id: postId,
        domain: window.location.origin  
      });
      
      if (response.success) {
        setPost(response.data.post);
      } else {
        setError('Post not found');
      }
    } catch (error) {
      console.error('Error fetching post details:', error);
      setError('Failed to load post');
    } finally {
      setLoading(false);
    }
  };

  const handleShare = () => {
    const currentUrl = window.location.href;
    
    if (navigator.share) {
      navigator.share({
        title: 'Check out this post',
        text: post.description || 'Shared from our platform',
        url: currentUrl,
      }).catch(console.error);
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(currentUrl).then(() => {
        toast.success('Link copied to clipboard!');
      }).catch(() => {
        toast.error('Failed to copy link');
      });
    }
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const renderMedia = (media) => {
    if (!media) return null;

    return (
      <div className="mb-3">
        {media.type === 'image' ? (
          <img 
            src={media.url} 
            alt="Post media" 
            className="img-fluid rounded"
            style={{ maxHeight: '500px', width: '100%', objectFit: 'cover' }}
          />
        ) : media.type === 'video' ? (
          <video 
            controls 
            className="w-100 rounded"
            style={{ maxHeight: '500px' }}
          >
            <source src={media.url} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : null}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3 text-muted">Loading post...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="text-center py-5">
              <Icon icon="mdi:alert-circle-outline" style={{ fontSize: '4rem', color: '#dc3545' }} />
              <h3 className="mt-3 text-danger">Error</h3>
              <p className="text-muted">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="text-center py-5">
              <Icon icon="mdi:post-outline" style={{ fontSize: '4rem', color: '#6c757d' }} />
              <h3 className="mt-3 text-muted">Post Not Found</h3>
              <p className="text-muted">The post you're looking for doesn't exist or has been removed.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-5">
      <div className="row justify-content-center">
        <div className="col-md-8">
          {/* Post Card */}
          <div className="card shadow-sm">
            <div className="card-body">
              {/* Post Header */}
              <div className="d-flex align-items-center mb-3">
                <img 
                  src={post.user_avatar || DefaultProfile} 
                  className="rounded-circle me-3" 
                  alt={post.user_name} 
                  style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                />
                <div className="flex-grow-1">
                  <h6 className="mb-0 fw-bold">{post.user_name}</h6>
                  <small className="text-muted">
                    {new Date(post.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </small>
                </div>
              </div>

              {/* Post Content */}
              {post.description && (
                <div className="mb-3">
                  {(() => {
                    const hasMedia = post.media_url && (post.media_type === 'image' || post.media_type === 'video');
                    
                    // For text-only posts, show full content
                    if (!hasMedia) {
                      return (
                        <p className="mb-0" style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                          {post.description}
                        </p>
                      );
                    }

                    // For posts with media, show truncated text with "Show more" option
                    const shouldTruncate = post.description.length > 100;
                    const displayText = showFullText ? post.description : post.description.substring(0, 100) + (shouldTruncate ? '...' : '');

                    return (
                      <p className="mb-0" style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                        {displayText}
                        {shouldTruncate && (
                          <button
                            className="btn btn-link p-0 ms-2 text-primary text-decoration-none"
                            onClick={() => setShowFullText(!showFullText)}
                          >
                            {showFullText ? 'Show less' : 'Show more'}
                          </button>
                        )}
                      </p>
                    );
                  })()}
                </div>
              )}

              {/* Post Media */}
              {post.media_url && renderMedia({
                type: post.media_type,
                url: post.media_url
              })}

              {/* Post Stats */}
              <div className="border-top pt-3 mt-3">
                <div className="d-flex justify-content-between">
                  {/* Like Button */}
                  <button 
                    className="btn btn-outline-secondary flex-fill me-2" 
                    disabled
                    style={{ opacity: 0.6, cursor: 'not-allowed' }}
                  >
                    <Icon icon="mdi:heart-outline" className="me-2" />
                    {post.likes_count || 0}
                  </button>
                  
                  {/* Comment Button */}
                  <button 
                    className="btn btn-outline-secondary flex-fill me-2" 
                    disabled
                    style={{ opacity: 0.6, cursor: 'not-allowed' }}
                  >
                    <Icon icon="mdi:comment-outline" className="me-2" />
                    {post.comments_count || 0}
                  </button>
                  
                  {/* Share Button */}
                  <button 
                    className="btn btn-primary flex-fill"
                    onClick={handleShare}
                  >
                    <Icon icon="mdi:share-variant" className="me-2" />
                    Share
                  </button>
                </div>
              </div>

              {/* Login Call-to-Action */}
              <div className="mt-3 bg-opacity-10">
                <div className="d-flex align-items-center justify-content-between">

                  <button 
                    className="btn btn-primary ms-3"
                    onClick={handleLogin}
                  >
                    <Icon icon="mdi:login" className="me-2" />
                    Login
                  </button>
                </div>
              </div>

              {/* Share Info */}
              <div className="mt-3 p-3 bg-light rounded">
                <small className="text-muted">
                  <Icon icon="mdi:information-outline" className="me-2" />
                  Login to like, comment, and engage with posts. Join our community to start sharing your thoughts!
                </small>
              </div>
            </div>
          </div>
              

        </div>
      </div>
    </div>
  );
};

export default PublicPostDetails;
