{"ast": null, "code": "import { POST, UPLOAD_FILE, UPLOAD_FILE_WITH_METHOD, PUT, DELETE } from './apiController';\nconst endpoint = \"/org/feed\";\n\n// --------------------------------- Feed Posts\n\n// Create Post\nexport const createPost = formData => {\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\n};\n\n// Edit Post\nexport const editPost = (postId, formData) => {\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/edit_post/${postId}`, formData, 'PUT');\n};\n\n// Delete Post\nexport const deletePost = postId => {\n  return DELETE(endpoint + `/delete_post/${postId}`);\n};\n\n// Get All Feeds with pagination\nexport const getAllFeeds = data => {\n  return POST(endpoint + \"/get_all_feeds\", data);\n};\n\n// Get My Feeds with pagination\nexport const getMyFeeds = data => {\n  return POST(endpoint + \"/get_my_feeds\", data);\n};\n\n// --------------------------------- Likes\n\n// Like/Unlike Post\nexport const likeUnlikePost = data => {\n  return POST(endpoint + \"/like_unlike_post\", data);\n};\n\n// --------------------------------- Comments\n\n// Add Comment\nexport const addComment = data => {\n  return POST(endpoint + \"/add_comment\", data);\n};\n\n// Edit Comment\nexport const editComment = (commentId, data) => {\n  return PUT(endpoint + `/edit_comment/${commentId}`, data);\n};\n\n// Delete Comment\nexport const deleteComment = commentId => {\n  return DELETE(endpoint + `/delete_comment/${commentId}`);\n};", "map": {"version": 3, "names": ["POST", "UPLOAD_FILE", "UPLOAD_FILE_WITH_METHOD", "PUT", "DELETE", "endpoint", "createPost", "formData", "editPost", "postId", "deletePost", "getAllFeeds", "data", "getMyFeeds", "likeUnlikePost", "addComment", "editComment", "commentId", "deleteComment"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/feedRoutes.js"], "sourcesContent": ["import { POST, UPLOAD_FILE, UPLOAD_FILE_WITH_METHOD, PUT, DELETE } from './apiController';\r\n\r\nconst endpoint = \"/org/feed\";\r\n\r\n// --------------------------------- Feed Posts\r\n\r\n// Create Post\r\nexport const createPost = (formData) => {\r\n  return UPLOAD_FILE(endpoint + \"/create_post\", formData);\r\n};\r\n\r\n// Edit Post\r\nexport const editPost = (postId, formData) => {\r\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/edit_post/${postId}`, formData, 'PUT');\r\n};\r\n\r\n// Delete Post\r\nexport const deletePost = (postId) => {\r\n  return DELETE(endpoint + `/delete_post/${postId}`);\r\n};\r\n\r\n// Get All Feeds with pagination\r\nexport const getAllFeeds = (data) => {\r\n  return POST(endpoint + \"/get_all_feeds\", data);\r\n};\r\n\r\n// Get My Feeds with pagination\r\nexport const getMyFeeds = (data) => {\r\n  return POST(endpoint + \"/get_my_feeds\", data);\r\n};\r\n\r\n// --------------------------------- Likes\r\n\r\n// Like/Unlike Post\r\nexport const likeUnlikePost = (data) => {\r\n  return POST(endpoint + \"/like_unlike_post\", data);\r\n};\r\n\r\n// --------------------------------- Comments\r\n\r\n// Add Comment\r\nexport const addComment = (data) => {\r\n  return POST(endpoint + \"/add_comment\", data);\r\n};\r\n\r\n// Edit Comment\r\nexport const editComment = (commentId, data) => {\r\n  return PUT(endpoint + `/edit_comment/${commentId}`, data);\r\n};\r\n\r\n// Delete Comment\r\nexport const deleteComment = (commentId) => {\r\n  return DELETE(endpoint + `/delete_comment/${commentId}`);\r\n};"], "mappings": "AAAA,SAASA,IAAI,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAEzF,MAAMC,QAAQ,GAAG,WAAW;;AAE5B;;AAEA;AACA,OAAO,MAAMC,UAAU,GAAIC,QAAQ,IAAK;EACtC,OAAON,WAAW,CAACI,QAAQ,GAAG,cAAc,EAAEE,QAAQ,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAACC,MAAM,EAAEF,QAAQ,KAAK;EAC5C,OAAOL,uBAAuB,CAACG,QAAQ,GAAG,cAAcI,MAAM,EAAE,EAAEF,QAAQ,EAAE,KAAK,CAAC;AACpF,CAAC;;AAED;AACA,OAAO,MAAMG,UAAU,GAAID,MAAM,IAAK;EACpC,OAAOL,MAAM,CAACC,QAAQ,GAAG,gBAAgBI,MAAM,EAAE,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAME,WAAW,GAAIC,IAAI,IAAK;EACnC,OAAOZ,IAAI,CAACK,QAAQ,GAAG,gBAAgB,EAAEO,IAAI,CAAC;AAChD,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAID,IAAI,IAAK;EAClC,OAAOZ,IAAI,CAACK,QAAQ,GAAG,eAAe,EAAEO,IAAI,CAAC;AAC/C,CAAC;;AAED;;AAEA;AACA,OAAO,MAAME,cAAc,GAAIF,IAAI,IAAK;EACtC,OAAOZ,IAAI,CAACK,QAAQ,GAAG,mBAAmB,EAAEO,IAAI,CAAC;AACnD,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMG,UAAU,GAAIH,IAAI,IAAK;EAClC,OAAOZ,IAAI,CAACK,QAAQ,GAAG,cAAc,EAAEO,IAAI,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMI,WAAW,GAAGA,CAACC,SAAS,EAAEL,IAAI,KAAK;EAC9C,OAAOT,GAAG,CAACE,QAAQ,GAAG,iBAAiBY,SAAS,EAAE,EAAEL,IAAI,CAAC;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMM,aAAa,GAAID,SAAS,IAAK;EAC1C,OAAOb,MAAM,CAACC,QAAQ,GAAG,mBAAmBY,SAAS,EAAE,CAAC;AAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}