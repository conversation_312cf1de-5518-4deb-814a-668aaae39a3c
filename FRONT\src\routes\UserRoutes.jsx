import React, { lazy } from 'react';
import { Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Import user pages
import UserLayout from '../layouts/UserLayout';
import UserDashboard from '../pages/user/dashboard/Dashboard';
import UserProfile from '../pages/user/profile/Profile';

import UserCourses from '../pages/user/course/Courses';
import CourseDetails from '../pages/user/course/CourseDetails';
import CourseWatch from '../pages/user/course/CourseWatch/CourseWatch';
import OrderDetails from '../pages/user/course/OrderDetails';
import OrderDetailsWithPayu from '../pages/user/course/OrderDetailsWithPayu';
import SuccessPayment from '../pages/user/settings/SuccessPayment';
import SuccessPayUPayment from '../pages/user/settings/SuccessPayUPayment';
import CancelPayUPayment from '../pages/user/settings/CancelPayUPayment';
import Result from '../pages/user/course/CourseWatch/Result';
import Resultvideo from '../pages/user/course/CourseWatch/Resultvideo';

import UserClassroom from '../pages/user/classroom/Classroom';
import AssignmentQuestions from '../pages/user/classroom/AssignmentQuestions';
import AssessmentQuestions from '../pages/user/classroom/AssessmentQuestions';
import AssessmentQuiz from '../pages/user/classroom/AssessmentQuiz';
import AssignmentQuizResult from '../pages/user/classroom/AssignmentQuizResult';
import UserCertificates from '../pages/user/certificates/Certificates';
import UserNotifications from '../pages/user/notification/Notifications';
import NotificationDetails from '../pages/user/notification/NotificationDetails';
import Settings from '../pages/user/settings/Settings';
import Help from '../pages/user/help/Help';
import Faq from '../pages/user/help/Faq';
import Ticket from '../pages/user/help/Ticket';
import AllClassroom from '../pages/user/classroom/AllClassroom';
import ZoomMeeting from '../components/zoom/ZoomMeeting';

import Feed from '../pages/user/feed/Feed';
import MyFeed from '../pages/user/feed/MyFeed';




function UserRoutes() {
  return (
    <>
      <Routes>
        {/* Standalone Assessment Quiz Route - No Layout */}
        <Route path="/user/assessmentQuiz/:encodedAssessmentId" element={<AssessmentQuiz />} />

        {/* Standalone Zoom Meeting Route - No Layout */}
        <Route path="/zoom-meeting" element={<ZoomMeeting />} />
        
        <Route path="/user" element={<UserLayout />}>

          {/* Dashboard */}
          <Route path="dashboard" element={<UserDashboard />} />

          {/* Profile */}
          <Route path="profile" element={<UserProfile />} />

          {/* All Classrooms */}
          <Route path="AllClassroom" element={<AllClassroom />} />
          <Route path="AllClassroom/classroomDetails/:encodedClassroomID" element={<UserClassroom />} />

          <Route path="assignmentsQuestions/:encodedAssignmentId" element={<AssignmentQuestions />} />

          <Route path="assessmentQuestions" element={<AssessmentQuestions />} />

          <Route path="assignmentQuizResult" element={<AssignmentQuizResult />} />

          

          {/* Courses */}
          <Route path="courses" element={<UserCourses />} />
          <Route path="courses/courseDetails/:encodedId" element={<CourseDetails />} />
          <Route path="courses/WatchCourse/:encodedId" element={<CourseWatch />} />
          <Route path="courses/orderDetails" element={<OrderDetails />} />
          <Route path="courses/orderDetailsWithPayu" element={<OrderDetailsWithPayu />} />
          <Route path="courses/successPayment" element={<SuccessPayment />} />
          <Route path="courses/successPayUPayment" element={<SuccessPayUPayment />} />
          <Route path="courses/cancelPayUPayment" element={<CancelPayUPayment />} />
          <Route path="courses/result" element={<Result />} />
          <Route path="resultvideo" element={<Resultvideo />} />


          


          {/* Certificates */}
          <Route path="certificates" element={<UserCertificates />} />

          {/* Notifications */}
          <Route path="notifications" element={<UserNotifications />} />
          <Route path="notifications/notificationDetails" element={<NotificationDetails />} />

          {/* Settings */}
          <Route path="settings" element={<Settings />} />

          {/* Help */}
          <Route path="help" element={<Help />} />
          <Route path="help/faq" element={<Faq />} />
          <Route path="help/ticket" element={<Ticket />} />

          {/* Feed */}
          <Route path="feed" element={<Feed />} />
          <Route path="my-feed" element={<MyFeed />} />
        </Route>
      </Routes>
      <ToastContainer />
    </>
  );
}

export default UserRoutes;

