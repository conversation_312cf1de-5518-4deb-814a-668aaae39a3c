{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\MyFeed.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport FeedPost from './FeedPost';\nimport { getMyPosts, toggleLike, addComment, getPostComments, generatePostShareUrl } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move ActionButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ActionButton = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  icon,\n  count,\n  onClick,\n  isLiked,\n  isLast,\n  buttonStyle,\n  actionButtonStyle\n}) => {\n  _s();\n  const buttonClass = useMemo(() => `btn border ${isLiked ? 'text-danger' : 'text-muted'}`, [isLiked]);\n  const buttonStyleMemo = useMemo(() => isLast ? {\n    ...actionButtonStyle,\n    marginRight: 0\n  } : actionButtonStyle, [isLast, actionButtonStyle]);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: buttonClass,\n    onClick: onClick,\n    style: buttonStyleMemo,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        icon: icon,\n        style: {\n          fontSize: '1.2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), count && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ms-1\",\n        style: {\n          fontSize: '0.9rem'\n        },\n        children: count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}, \"BW3KalXNlo6xULmJhXkh9aylRuM=\")), \"BW3KalXNlo6xULmJhXkh9aylRuM=\");\n_c2 = ActionButton;\nconst MyFeed = () => {\n  _s2();\n  // Posts state\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [postingNewPost, setPostingNewPost] = useState(false);\n  const [userProfile, setUserProfile] = useState(null);\n\n  // Comments state\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  // Load my posts\n  const loadMyPosts = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setLoading(true);\n      } else {\n        setLoadingMore(true);\n      }\n      const response = await getMyPosts(page, 5);\n      if (response.success) {\n        const transformedPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n        if (append) {\n          setPosts(prev => [...prev, ...transformedPosts]);\n        } else {\n          setPosts(transformedPosts);\n          // Store user profile from first post\n          if (transformedPosts.length > 0) {\n            setUserProfile({\n              name: transformedPosts[0].user.name,\n              profile_pic_url: transformedPosts[0].user.avatar\n            });\n          }\n        }\n        setCurrentPage(page);\n        setHasMore(response.data.pagination.has_more);\n      } else {\n        toast.error('Failed to load posts');\n      }\n    } catch (error) {\n      console.error('Error loading posts:', error);\n      toast.error('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setLoadingMore(false);\n    }\n  }, []);\n\n  // Load initial posts\n  useEffect(() => {\n    loadMyPosts(1);\n  }, [loadMyPosts]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadingMore, hasMore]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      } else {\n        setLoadingMoreComments(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      }\n      const response = await getPostComments(postId, page, 10);\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString()\n        }));\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n        setCommentsPage(prev => ({\n          ...prev,\n          [postId]: page\n        }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n      setLoadingMoreComments(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n    }\n  }, []);\n\n  // Event handlers\n  const handleLike = async postId => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          isLiked: !post.isLiked,\n          likes: post.isLiked ? post.likes - 1 : post.likes + 1\n        } : post));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n  const handleComment = postId => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({\n      ...prev,\n      [postId]: !prev[postId]\n    }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n  const loadMoreComments = postId => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n  const handleSubmitComment = async postId => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    if (commentText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount + 1\n        } : post));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now'\n        };\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n        setNewComment(prev => ({\n          ...prev,\n          [postId]: ''\n        }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handlePostSubmit = async newPost => {\n    console.log('handlePostSubmit called with:', newPost);\n    setPostingNewPost(true);\n\n    // Simulate API delay and then refresh the feed\n    setTimeout(async () => {\n      try {\n        await loadMyPosts(1); // Reload posts to include the new one\n        toast.success('Post created successfully!');\n      } finally {\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', {\n        currentPage: currentPage + 1,\n        hasMore\n      });\n      loadMyPosts(currentPage + 1, true);\n    }\n  }, [loadMyPosts, loadingMore, hasMore, currentPage]);\n  const handleShare = async post => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    const mediaStyle = {\n      width: '100%',\n      maxHeight: '400px'\n    };\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"img\", {\n        src: media.url,\n        className: \"img-fluid rounded\",\n        alt: \"Post media\",\n        style: {\n          ...mediaStyle,\n          objectFit: 'contain'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 14\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"video\", {\n        className: \"img-fluid rounded\",\n        controls: true,\n        style: mediaStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: media.url,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text mb-2\",\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this);\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"card-text mb-2\",\n        children: [displayText, shouldTruncate && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-link p-0 ms-2 text-primary text-decoration-none\",\n          onClick: () => setShowFullText(prev => ({\n            ...prev,\n            [post.id]: !isShowingFull\n          })),\n          children: isShowingFull ? 'Show less' : 'Show more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({\n      ...prev,\n      [postId]: value\n    }));\n  }, []);\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, []);\n  const handleCommentSubmitClick = useCallback(postId => {\n    handleSubmitComment(postId);\n  }, []);\n  const renderComments = post => {\n    if (!showComments[post.id]) return null;\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-top pt-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-3\",\n        children: [\"Comments (\", post.commentsCount || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n          className: \"rounded-circle me-2\",\n          alt: \"Profile\",\n          style: {\n            width: '32px',\n            height: '32px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Write a comment...\",\n            value: newComment[post.id] || '',\n            onChange: e => handleCommentChange(post.id, e.target.value),\n            onKeyDown: e => handleCommentKeyDown(e, post.id),\n            maxLength: 400\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-1\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: (newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n              children: [(newComment[post.id] || '').length, \"/400 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm ms-2 w-auto\",\n          onClick: () => handleCommentSubmitClick(post.id),\n          disabled: !newComment[post.id] || !newComment[post.id].trim(),\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border spinner-border-sm\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted small\",\n          children: \"Loading comments...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          id: `comments-container-${post.id}`,\n          children: [comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: comment.avatar,\n              className: \"rounded-circle me-2\",\n              alt: comment.user,\n              style: {\n                width: '32px',\n                height: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-light rounded p-2 flex-grow-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fw-bold\",\n                children: comment.user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: comment.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-muted small mt-1\",\n                children: comment.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this)]\n          }, comment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 17\n          }, this)), hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-link text-muted p-0 text-decoration-none\",\n              onClick: () => loadMoreComments(post.id),\n              disabled: isLoadingMore,\n              children: isLoadingMore ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"spinner-border spinner-border-sm me-2\",\n                  role: \"status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"visually-hidden\",\n                    children: \"Loading...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 25\n                }, this), \"Loading more comments...\"]\n              }, void 0, true) : 'Load more comments'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-light rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0 small\",\n              children: \"This is your personal feed where you can see all your posts and updates. Create new posts below and manage your content here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-end me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"My Posts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Your personal posts and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n              className: \"rounded-circle\",\n              alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"Profile\",\n              style: {\n                width: '50px',\n                height: '50px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeedPost, {\n          onPostSubmit: handlePostSubmit,\n          userProfile: userProfile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), postingNewPost && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          style: {\n            animation: 'fadeIn 0.5s ease-in-out',\n            border: '2px dashed #007bff',\n            backgroundColor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-3\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Creating post...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-2\",\n              children: \"Creating your post...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Please wait while we process your content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"Loading your posts...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:post-outline\",\n            style: {\n              fontSize: '3rem',\n              color: '#6c757d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"No posts yet. Be the first to share something!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [posts.map((post, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: post.user.avatar,\n                  className: \"rounded-circle me-3\",\n                  alt: post.user.name,\n                  style: {\n                    width: '40px',\n                    height: '40px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: post.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: new Date(post.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [renderPostContent(post.content, post), renderMedia(post.media)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\",\n                  count: post.likes,\n                  onClick: () => handleLike(post.id),\n                  isLiked: post.isLiked,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:comment-outline\",\n                  count: post.commentsCount || 0,\n                  onClick: () => handleComment(post.id),\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:share-variant-outline\",\n                  onClick: () => handleShare(post),\n                  isLast: true,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this), renderComments(post)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this)), loadingMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more posts...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-primary mb-0\",\n              children: \"Loading more posts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 17\n          }, this), !hasMore && posts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"You've reached the end of your posts!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 504,\n    columnNumber: 5\n  }, this);\n};\n_s2(MyFeed, \"19qL4Jbq1z3uQ+YVQ1XMz5aRKgw=\");\n_c3 = MyFeed;\nexport default MyFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ActionButton$React.memo\");\n$RefreshReg$(_c2, \"ActionButton\");\n$RefreshReg$(_c3, \"MyFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Icon", "DefaultProfile", "FeedPost", "getMyPosts", "toggleLike", "addComment", "getPostComments", "generatePostShareUrl", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ActionButton", "_s", "memo", "_c", "icon", "count", "onClick", "isLiked", "isLast", "buttonStyle", "actionButtonStyle", "buttonClass", "buttonStyleMemo", "marginRight", "className", "style", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "MyFeed", "_s2", "posts", "setPosts", "loading", "setLoading", "loadingMore", "setLoadingMore", "currentPage", "setCurrentPage", "hasMore", "setHasMore", "postingNewPost", "setPostingNewPost", "userProfile", "setUserProfile", "newComment", "setNewComment", "showComments", "setShowComments", "postComments", "setPostComments", "commentsLoading", "setCommentsLoading", "commentsPage", "setCommentsPage", "commentsHasMore", "setCommentsHasMore", "loadingMoreComments", "setLoadingMoreComments", "showFullText", "setShowFullText", "loadMyPosts", "page", "append", "response", "success", "transformedPosts", "data", "map", "post", "id", "user", "name", "user_name", "avatar", "user_avatar", "content", "description", "media", "media_url", "type", "media_type", "url", "is_liked_by_user", "likes", "likes_count", "commentsCount", "comments_count", "created_at", "prev", "length", "profile_pic_url", "pagination", "has_more", "error", "console", "handleScroll", "scrollTop", "document", "documentElement", "scrollHeight", "clientHeight", "loadMorePosts", "window", "addEventListener", "removeEventListener", "backgroundColor", "borderColor", "flex", "loadPostComments", "postId", "newComments", "comments", "comment", "text", "timestamp", "Date", "commented_at", "toLocaleDateString", "handleLike", "handleComment", "isOpening", "loadMoreComments", "handleSubmitComment", "commentText", "trim", "newCommentObj", "handlePostSubmit", "newPost", "log", "setTimeout", "handleShare", "shareUrl", "shareData", "title", "navigator", "share", "clipboard", "writeText", "renderMedia", "mediaStyle", "width", "maxHeight", "src", "alt", "objectFit", "controls", "renderPostContent", "hasMedia", "shouldTruncate", "isShowingFull", "displayText", "substring", "handleCommentChange", "value", "handleCommentKeyDown", "e", "key", "handleCommentSubmitClick", "renderComments", "isLoading", "isLoadingMore", "height", "placeholder", "onChange", "target", "onKeyDown", "max<PERSON><PERSON><PERSON>", "disabled", "role", "overflowY", "onPostSubmit", "animation", "border", "color", "index", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/MyFeed.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react'\nimport { Icon } from '@iconify/react'\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\nimport FeedPost from './FeedPost'\nimport { getMyPosts, toggleLike, addComment, getPostComments, generatePostShareUrl } from '../../../services/feedServices'\nimport { toast } from 'react-toastify'\n\n// Move ActionButton outside to prevent recreation on every render\nconst ActionButton = React.memo(({ icon, count, onClick, isLiked, isLast, buttonStyle, actionButtonStyle }) => {\n  const buttonClass = useMemo(() =>\n    `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,\n    [isLiked]\n  );\n\n  const buttonStyleMemo = useMemo(() =>\n    isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle,\n    [isLast, actionButtonStyle]\n  );\n\n  return (\n    <button\n      className={buttonClass}\n      onClick={onClick}\n      style={buttonStyleMemo}\n    >\n      <div className=\"d-flex align-items-center justify-content-center\">\n        <Icon icon={icon} style={{fontSize: '1.2rem'}} />\n        {count && <span className=\"ms-1\" style={{fontSize: '0.9rem'}}>{count}</span>}\n      </div>\n    </button>\n  );\n});\n\nconst MyFeed = () => {\n  // Posts state\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [postingNewPost, setPostingNewPost] = useState(false);\n  const [userProfile, setUserProfile] = useState(null);\n\n  // Comments state\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  // Load my posts\n  const loadMyPosts = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setLoading(true);\n      } else {\n        setLoadingMore(true);\n      }\n\n      const response = await getMyPosts(page, 5);\n\n      if (response.success) {\n        const transformedPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n\n        if (append) {\n          setPosts(prev => [...prev, ...transformedPosts]);\n        } else {\n          setPosts(transformedPosts);\n          // Store user profile from first post\n          if (transformedPosts.length > 0) {\n            setUserProfile({\n              name: transformedPosts[0].user.name,\n              profile_pic_url: transformedPosts[0].user.avatar\n            });\n          }\n        }\n\n        setCurrentPage(page);\n        setHasMore(response.data.pagination.has_more);\n      } else {\n        toast.error('Failed to load posts');\n      }\n    } catch (error) {\n      console.error('Error loading posts:', error);\n      toast.error('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setLoadingMore(false);\n    }\n  }, []);\n\n  // Load initial posts\n  useEffect(() => {\n    loadMyPosts(1);\n  }, [loadMyPosts]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n      \n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadingMore, hasMore]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({ ...prev, [postId]: true }));\n      } else {\n        setLoadingMoreComments(prev => ({ ...prev, [postId]: true }));\n      }\n\n      const response = await getPostComments(postId, page, 10);\n\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString()\n        }));\n\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n\n        setCommentsPage(prev => ({ ...prev, [postId]: page }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({ ...prev, [postId]: false }));\n      setLoadingMoreComments(prev => ({ ...prev, [postId]: false }));\n    }\n  }, []);\n\n  // Event handlers\n  const handleLike = async (postId) => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? {\n                ...post,\n                isLiked: !post.isLiked,\n                likes: post.isLiked ? post.likes - 1 : post.likes + 1\n              }\n            : post\n        ));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n\n  const handleComment = (postId) => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n\n  const loadMoreComments = (postId) => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n\n  const handleSubmitComment = async (postId) => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n\n    if (commentText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? { ...post, commentsCount: post.commentsCount + 1 }\n            : post\n        ));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now'\n        };\n\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n\n  const handlePostSubmit = async (newPost) => {\n    console.log('handlePostSubmit called with:', newPost);\n    setPostingNewPost(true);\n\n    // Simulate API delay and then refresh the feed\n    setTimeout(async () => {\n      try {\n        await loadMyPosts(1); // Reload posts to include the new one\n        toast.success('Post created successfully!');\n      } finally {\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });\n      loadMyPosts(currentPage + 1, true);\n    }\n  }, [loadMyPosts, loadingMore, hasMore, currentPage]);\n\n  const handleShare = async (post) => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = (media) => {\n    if (!media) return null;\n\n    const mediaStyle = { width: '100%', maxHeight: '400px' };\n\n    if (media.type === 'image') {\n      return <img src={media.url} className=\"img-fluid rounded\" alt=\"Post media\" style={{...mediaStyle, objectFit: 'contain'}} />;\n    } else if (media.type === 'video') {\n      return (\n        <video className=\"img-fluid rounded\" controls style={mediaStyle}>\n          <source src={media.url} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n      );\n    }\n    return null;\n  };\n\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return (\n        <div>\n          <p className=\"card-text mb-2\">{content}</p>\n        </div>\n      );\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n\n    return (\n      <div>\n        <p className=\"card-text mb-2\">\n          {displayText}\n          {shouldTruncate && (\n            <button\n              className=\"btn btn-link p-0 ms-2 text-primary text-decoration-none\"\n              onClick={() => setShowFullText(prev => ({ ...prev, [post.id]: !isShowingFull }))}\n            >\n              {isShowingFull ? 'Show less' : 'Show more'}\n            </button>\n          )}\n        </p>\n      </div>\n    );\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({ ...prev, [postId]: value }));\n  }, []);\n\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, []);\n\n  const handleCommentSubmitClick = useCallback((postId) => {\n    handleSubmitComment(postId);\n  }, []);\n\n  const renderComments = (post) => {\n    if (!showComments[post.id]) return null;\n\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n\n    return (\n      <div className=\"border-top pt-3 mt-3\">\n        <h6 className=\"mb-3\">Comments ({post.commentsCount || 0})</h6>\n\n        {/* Comment Input */}\n        <div className=\"d-flex mb-3\">\n          <img src={userProfile?.profile_pic_url || DefaultProfile} className=\"rounded-circle me-2\" alt=\"Profile\" style={{width: '32px', height: '32px'}} />\n          <div className=\"flex-grow-1\">\n            <input\n              type=\"text\"\n              className=\"form-control\"\n              placeholder=\"Write a comment...\"\n              value={newComment[post.id] || ''}\n              onChange={(e) => handleCommentChange(post.id, e.target.value)}\n              onKeyDown={(e) => handleCommentKeyDown(e, post.id)}\n              maxLength={400}\n            />\n            <div className=\"d-flex justify-content-end mt-1\">\n              <small className={(newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\n                {(newComment[post.id] || '').length}/400 characters\n              </small>\n            </div>\n          </div>\n          <button\n            className=\"btn btn-primary btn-sm ms-2 w-auto\"\n            onClick={() => handleCommentSubmitClick(post.id)}\n            disabled={!newComment[post.id] || !newComment[post.id].trim()}\n          >\n            <Icon icon=\"mdi:send\" />\n          </button>\n        </div>\n\n        {/* Comments Loading State */}\n        {isLoading ? (\n          <div className=\"text-center py-3\">\n            <div className=\"spinner-border spinner-border-sm\" role=\"status\">\n              <span className=\"visually-hidden\">Loading comments...</span>\n            </div>\n            <p className=\"mt-2 text-muted small\">Loading comments...</p>\n          </div>\n        ) : (\n          <>\n            {/* Comments Container with Scroll */}\n            <div style={{ maxHeight: '300px', overflowY: 'auto' }} id={`comments-container-${post.id}`}>\n              {/* Existing Comments */}\n              {comments.map(comment => (\n                <div key={comment.id} className=\"d-flex mb-2\">\n                  <img src={comment.avatar} className=\"rounded-circle me-2\" alt={comment.user} style={{width: '32px', height: '32px'}} />\n                  <div className=\"bg-light rounded p-2 flex-grow-1\">\n                    <div className=\"fw-bold\">{comment.user}</div>\n                    <div>{comment.text}</div>\n                    <div className=\"text-muted small mt-1\">{comment.timestamp}</div>\n                  </div>\n                </div>\n              ))}\n\n              {/* Load More Comments Button */}\n              {hasMore && (\n                <div className=\"text-center mt-2\">\n                  <button\n                    className=\"btn btn-link text-muted p-0 text-decoration-none\"\n                    onClick={() => loadMoreComments(post.id)}\n                    disabled={isLoadingMore}\n                  >\n                    {isLoadingMore ? (\n                      <>\n                        <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                          <span className=\"visually-hidden\">Loading...</span>\n                        </div>\n                        Loading more comments...\n                      </>\n                    ) : (\n                      'Load more comments'\n                    )}\n                  </button>\n                </div>\n              )}\n            </div>\n          </>\n        )}\n      </div>\n    );\n  };\n\n\n\n  return (\n    <div className=\"container py-4\">\n      <div className=\"row justify-content-center\">\n        <div className=\"col-md-8\">\n                    {/* Feed Info */}\n                    <div className=\"text-center mb-4\">\n            <div className=\"p-3 bg-light rounded\">\n              <p className=\"text-muted mb-0 small\">\n                This is your personal feed where you can see all your posts and updates. \n                Create new posts below and manage your content here.\n              </p>\n            </div>\n          </div>\n\n          {/* Profile Header */}\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <div></div>\n            <div className=\"d-flex align-items-center\">\n              <div className=\"text-end me-3\">\n                <h5 className=\"mb-0\">My Posts</h5>\n                <small className=\"text-muted\">Your personal posts and updates</small>\n              </div>\n              <img \n                src={userProfile?.profile_pic_url || DefaultProfile} \n                className=\"rounded-circle\" \n                alt={userProfile?.name || \"Profile\"} \n                style={{width: '50px', height: '50px'}} \n              />\n            </div>\n          </div>\n\n          {/* Create Post Component */}\n          <FeedPost onPostSubmit={handlePostSubmit} userProfile={userProfile} />\n\n\n          {/* New Post Loading State */}\n          {postingNewPost && (\n            <div className=\"card mb-4\" style={{\n              animation: 'fadeIn 0.5s ease-in-out',\n              border: '2px dashed #007bff',\n              backgroundColor: '#f8f9fa'\n            }}>\n              <div className=\"card-body text-center py-4\">\n                <div className=\"spinner-border text-primary mb-3\" role=\"status\">\n                  <span className=\"visually-hidden\">Creating post...</span>\n                </div>\n                <h6 className=\"text-primary mb-2\">Creating your post...</h6>\n                <p className=\"text-muted mb-0\">Please wait while we process your content</p>\n              </div>\n            </div>\n          )}\n\n          {/* Loading State */}\n          {loading ? (\n            <div className=\"text-center py-4\">\n              <div className=\"spinner-border\" role=\"status\">\n                <span className=\"visually-hidden\">Loading...</span>\n              </div>\n              <p className=\"mt-2 text-muted\">Loading your posts...</p>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <Icon icon=\"mdi:post-outline\" style={{ fontSize: '3rem', color: '#6c757d' }} />\n              <p className=\"mt-2 text-muted\">No posts yet. Be the first to share something!</p>\n            </div>\n          ) : (\n            <>\n              {/* Posts Feed */}\n              {posts.map((post, index) => (\n              <div key={post.id} className=\"card mb-4\">\n                <div className=\"card-body\">\n                  {/* Post Header */}\n                  <div className=\"d-flex align-items-center mb-3\">\n                    <img src={post.user.avatar} className=\"rounded-circle me-3\" alt={post.user.name} style={{width: '40px', height: '40px'}} />\n                    <div className=\"flex-grow-1\">\n                      <h6 className=\"mb-0\">{post.user.name}</h6>\n                      <small className=\"text-muted\">{new Date(post.created_at).toLocaleDateString()}</small>\n                    </div>\n                  </div>\n\n                  {/* Post Content */}\n                  <div className=\"mb-3\">\n                    {renderPostContent(post.content, post)}\n                    {renderMedia(post.media)}\n                  </div>\n\n                  {/* Action Buttons */}\n                  <div className=\"d-flex justify-content-between\">\n                    <ActionButton\n                      icon={post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\"}\n                      count={post.likes}\n                      onClick={() => handleLike(post.id)}\n                      isLiked={post.isLiked}\n                      buttonStyle={buttonStyle}\n                      actionButtonStyle={actionButtonStyle}\n                    />\n                    <ActionButton\n                      icon=\"mdi:comment-outline\"\n                      count={post.commentsCount || 0}\n                      onClick={() => handleComment(post.id)}\n                      buttonStyle={buttonStyle}\n                      actionButtonStyle={actionButtonStyle}\n                    />\n                    <ActionButton\n                      icon=\"mdi:share-variant-outline\"\n                      onClick={() => handleShare(post)}\n                      isLast={true}\n                      buttonStyle={buttonStyle}\n                      actionButtonStyle={actionButtonStyle}\n                    />\n                  </div>\n\n                  {/* Comments Section */}\n                  {renderComments(post)}\n                </div>\n              </div>\n              ))}\n\n              {/* Loading More Posts Indicator */}\n              {loadingMore && (\n                <div className=\"text-center py-4\">\n                  <div className=\"spinner-border text-primary mb-2\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading more posts...</span>\n                  </div>\n                  <p className=\"text-primary mb-0\">Loading more posts...</p>\n                </div>\n              )}\n\n              {!hasMore && posts.length > 0 && (\n                <div className=\"text-center py-3\">\n                  <p className=\"text-muted\">You've reached the end of your posts!</p>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MyFeed; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,oBAAoB,QAAQ,gCAAgC;AAC1H,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,gBAAAC,EAAA,cAAGnB,KAAK,CAACoB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,WAAW;EAAEC;AAAkB,CAAC,KAAK;EAAAT,EAAA;EAC7G,MAAMU,WAAW,GAAGzB,OAAO,CAAC,MAC1B,cAAcqB,OAAO,GAAG,aAAa,GAAG,YAAY,EAAE,EACtD,CAACA,OAAO,CACV,CAAC;EAED,MAAMK,eAAe,GAAG1B,OAAO,CAAC,MAC9BsB,MAAM,GAAG;IAAE,GAAGE,iBAAiB;IAAEG,WAAW,EAAE;EAAE,CAAC,GAAGH,iBAAiB,EACrE,CAACF,MAAM,EAAEE,iBAAiB,CAC5B,CAAC;EAED,oBACEb,OAAA;IACEiB,SAAS,EAAEH,WAAY;IACvBL,OAAO,EAAEA,OAAQ;IACjBS,KAAK,EAAEH,eAAgB;IAAAI,QAAA,eAEvBnB,OAAA;MAAKiB,SAAS,EAAC,kDAAkD;MAAAE,QAAA,gBAC/DnB,OAAA,CAACV,IAAI;QAACiB,IAAI,EAAEA,IAAK;QAACW,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChDhB,KAAK,iBAAIR,OAAA;QAAMiB,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ,CAAE;QAAAD,QAAA,EAAEX;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC,kCAAC;AAACC,GAAA,GAvBGtB,YAAY;AAyBlB,MAAMuB,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMwE,WAAW,GAAGtE,WAAW,CAAC,OAAOuE,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAClE,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE;QACd5B,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLE,cAAc,CAAC,IAAI,CAAC;MACtB;MAEA,MAAM4B,QAAQ,GAAG,MAAMpE,UAAU,CAACkE,IAAI,EAAE,CAAC,CAAC;MAE1C,IAAIE,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMC,gBAAgB,GAAGF,QAAQ,CAACG,IAAI,CAACpC,KAAK,CAACqC,GAAG,CAACC,IAAI,KAAK;UACxDC,EAAE,EAAED,IAAI,CAACC,EAAE;UACXC,IAAI,EAAE;YACJC,IAAI,EAAEH,IAAI,CAACI,SAAS;YACpBC,MAAM,EAAEL,IAAI,CAACM,WAAW,IAAIjF;UAC9B,CAAC;UACDkF,OAAO,EAAEP,IAAI,CAACQ,WAAW;UACzBC,KAAK,EAAET,IAAI,CAACU,SAAS,GAAG;YACtBC,IAAI,EAAEX,IAAI,CAACY,UAAU;YACrBC,GAAG,EAAEb,IAAI,CAACU;UACZ,CAAC,GAAG,IAAI;UACRlE,OAAO,EAAEwD,IAAI,CAACc,gBAAgB,KAAK,CAAC;UACpCC,KAAK,EAAEf,IAAI,CAACgB,WAAW;UACvBC,aAAa,EAAEjB,IAAI,CAACkB,cAAc;UAClCC,UAAU,EAAEnB,IAAI,CAACmB;QACnB,CAAC,CAAC,CAAC;QAEH,IAAIzB,MAAM,EAAE;UACV/B,QAAQ,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGvB,gBAAgB,CAAC,CAAC;QAClD,CAAC,MAAM;UACLlC,QAAQ,CAACkC,gBAAgB,CAAC;UAC1B;UACA,IAAIA,gBAAgB,CAACwB,MAAM,GAAG,CAAC,EAAE;YAC/B9C,cAAc,CAAC;cACb4B,IAAI,EAAEN,gBAAgB,CAAC,CAAC,CAAC,CAACK,IAAI,CAACC,IAAI;cACnCmB,eAAe,EAAEzB,gBAAgB,CAAC,CAAC,CAAC,CAACK,IAAI,CAACG;YAC5C,CAAC,CAAC;UACJ;QACF;QAEApC,cAAc,CAACwB,IAAI,CAAC;QACpBtB,UAAU,CAACwB,QAAQ,CAACG,IAAI,CAACyB,UAAU,CAACC,QAAQ,CAAC;MAC/C,CAAC,MAAM;QACL5F,KAAK,CAAC6F,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C7F,KAAK,CAAC6F,KAAK,CAAC,sBAAsB,CAAC;IACrC,CAAC,SAAS;MACR5D,UAAU,CAAC,KAAK,CAAC;MACjBE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9C,SAAS,CAAC,MAAM;IACduE,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACAvE,SAAS,CAAC,MAAM;IACd,MAAM0G,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAACF,SAAS;MACpD,MAAMG,YAAY,GAAGF,QAAQ,CAACC,eAAe,CAACC,YAAY;MAC1D,MAAMC,YAAY,GAAGH,QAAQ,CAACC,eAAe,CAACE,YAAY;;MAE1D;MACA,IAAIJ,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,GAAG,EAAE;QAClD,IAAI,CAACjE,WAAW,IAAII,OAAO,EAAE;UAC3B+D,aAAa,CAAC,CAAC;QACjB;MACF;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAER,YAAY,CAAC;IAC/C,OAAO,MAAMO,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;EACjE,CAAC,EAAE,CAAC7D,WAAW,EAAEI,OAAO,CAAC,CAAC;;EAE1B;EACA;EACA,MAAMxB,WAAW,GAAGvB,OAAO,CAAC,OAAO;IACjCkH,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAM3F,iBAAiB,GAAGxB,OAAO,CAAC,OAAO;IACvCoH,IAAI,EAAE,CAAC;IACPzF,WAAW,EAAE,MAAM;IACnB,GAAGJ;EACL,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAElB;EACA,MAAM8F,gBAAgB,GAAGtH,WAAW,CAAC,OAAOuH,MAAM,EAAEhD,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAC/E,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE;QACdV,kBAAkB,CAACqC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC3D,CAAC,MAAM;QACLpD,sBAAsB,CAAC+B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC/D;MAEA,MAAM9C,QAAQ,GAAG,MAAMjE,eAAe,CAAC+G,MAAM,EAAEhD,IAAI,EAAE,EAAE,CAAC;MAExD,IAAIE,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAM8C,WAAW,GAAG/C,QAAQ,CAACG,IAAI,CAAC6C,QAAQ,CAAC5C,GAAG,CAAC6C,OAAO,KAAK;UACzD3C,EAAE,EAAE2C,OAAO,CAAC3C,EAAE;UACdC,IAAI,EAAE0C,OAAO,CAACxC,SAAS;UACvBC,MAAM,EAAEuC,OAAO,CAACtC,WAAW,IAAIjF,cAAc;UAC7CwH,IAAI,EAAED,OAAO,CAACA,OAAO;UACrBE,SAAS,EAAE,IAAIC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAIvD,MAAM,EAAE;UACVb,eAAe,CAACuC,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACqB,MAAM,GAAG,CAAC,IAAIrB,IAAI,CAACqB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAGC,WAAW;UACpD,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL7D,eAAe,CAACuC,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACqB,MAAM,GAAGC;UACZ,CAAC,CAAC,CAAC;QACL;QAEAzD,eAAe,CAACmC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAGhD;QAAK,CAAC,CAAC,CAAC;QACtDN,kBAAkB,CAACiC,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAG9C,QAAQ,CAACG,IAAI,CAACyB,UAAU,CAACC;QACrC,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL5F,KAAK,CAAC6F,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7F,KAAK,CAAC6F,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACR1C,kBAAkB,CAACqC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;MAC1DpD,sBAAsB,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,UAAU,GAAG,MAAOT,MAAM,IAAK;IACnC,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAMnE,UAAU,CAACiH,MAAM,CAAC;MACzC,IAAI9C,QAAQ,CAACC,OAAO,EAAE;QACpBjC,QAAQ,CAACD,KAAK,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACC,EAAE,KAAKwC,MAAM,GACd;UACE,GAAGzC,IAAI;UACPxD,OAAO,EAAE,CAACwD,IAAI,CAACxD,OAAO;UACtBuE,KAAK,EAAEf,IAAI,CAACxD,OAAO,GAAGwD,IAAI,CAACe,KAAK,GAAG,CAAC,GAAGf,IAAI,CAACe,KAAK,GAAG;QACtD,CAAC,GACDf,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpE,KAAK,CAAC6F,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C7F,KAAK,CAAC6F,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAM0B,aAAa,GAAIV,MAAM,IAAK;IAChC,MAAMW,SAAS,GAAG,CAAC1E,YAAY,CAAC+D,MAAM,CAAC;IACvC9D,eAAe,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACqB,MAAM,GAAG,CAACrB,IAAI,CAACqB,MAAM;IAAE,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIW,SAAS,IAAI,CAACxE,YAAY,CAAC6D,MAAM,CAAC,EAAE;MACtCD,gBAAgB,CAACC,MAAM,EAAE,CAAC,CAAC;IAC7B;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAIZ,MAAM,IAAK;IACnC,MAAMzE,WAAW,GAAGgB,YAAY,CAACyD,MAAM,CAAC,IAAI,CAAC;IAC7CD,gBAAgB,CAACC,MAAM,EAAEzE,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;EACjD,CAAC;EAED,MAAMsF,mBAAmB,GAAG,MAAOb,MAAM,IAAK;IAC5C,MAAMc,WAAW,GAAG/E,UAAU,CAACiE,MAAM,CAAC;IACtC,IAAI,CAACc,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;MACvC5H,KAAK,CAAC6F,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAI8B,WAAW,CAAClC,MAAM,GAAG,GAAG,EAAE;MAC5BzF,KAAK,CAAC6F,KAAK,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMlE,UAAU,CAACgH,MAAM,EAAEc,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7D,IAAI7D,QAAQ,CAACC,OAAO,EAAE;QACpB;QACAjC,QAAQ,CAACD,KAAK,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACC,EAAE,KAAKwC,MAAM,GACd;UAAE,GAAGzC,IAAI;UAAEiB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAG;QAAE,CAAC,GAClDjB,IACN,CAAC,CAAC;;QAEF;QACA,MAAMyD,aAAa,GAAG;UACpBxD,EAAE,EAAEN,QAAQ,CAACG,IAAI,CAAC8C,OAAO,CAAC3C,EAAE;UAC5BC,IAAI,EAAEP,QAAQ,CAACG,IAAI,CAAC8C,OAAO,CAACxC,SAAS;UACrCC,MAAM,EAAEV,QAAQ,CAACG,IAAI,CAAC8C,OAAO,CAACtC,WAAW,IAAIjF,cAAc;UAC3DwH,IAAI,EAAElD,QAAQ,CAACG,IAAI,CAAC8C,OAAO,CAACA,OAAO;UACnCE,SAAS,EAAE;QACb,CAAC;QAEDjE,eAAe,CAACuC,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAG,CAACgB,aAAa,EAAE,IAAIrC,IAAI,CAACqB,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEHhE,aAAa,CAAC2C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAG,CAAC,CAAC,CAAC;QAClD7G,KAAK,CAACgE,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,MAAM;QACLhE,KAAK,CAAC6F,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C7F,KAAK,CAAC6F,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1CjC,OAAO,CAACkC,GAAG,CAAC,+BAA+B,EAAED,OAAO,CAAC;IACrDtF,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACAwF,UAAU,CAAC,YAAY;MACrB,IAAI;QACF,MAAMrE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB5D,KAAK,CAACgE,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,SAAS;QACRvB,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM4D,aAAa,GAAG/G,WAAW,CAAC,MAAM;IACtC,IAAI,CAAC4C,WAAW,IAAII,OAAO,EAAE;MAC3BwD,OAAO,CAACkC,GAAG,CAAC,uBAAuB,EAAE;QAAE5F,WAAW,EAAEA,WAAW,GAAG,CAAC;QAAEE;MAAQ,CAAC,CAAC;MAC/EsB,WAAW,CAACxB,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;IACpC;EACF,CAAC,EAAE,CAACwB,WAAW,EAAE1B,WAAW,EAAEI,OAAO,EAAEF,WAAW,CAAC,CAAC;EAEpD,MAAM8F,WAAW,GAAG,MAAO9D,IAAI,IAAK;IAClC,IAAI;MACF;MACA,MAAML,QAAQ,GAAG,MAAMhE,oBAAoB,CAACqE,IAAI,CAACC,EAAE,CAAC;MAEpD,IAAIN,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMmE,QAAQ,GAAGpE,QAAQ,CAACG,IAAI,CAACiE,QAAQ;;QAEvC;QACA,MAAMC,SAAS,GAAG;UAChBC,KAAK,EAAE,GAAGjE,IAAI,CAACE,IAAI,CAACC,IAAI,SAAS;UACjC0C,IAAI,EAAE7C,IAAI,CAACO,OAAO,IAAI,sBAAsB;UAC5CM,GAAG,EAAEkD;QACP,CAAC;;QAED;QACA,IAAIG,SAAS,CAACC,KAAK,EAAE;UACnB,MAAMD,SAAS,CAACC,KAAK,CAACH,SAAS,CAAC;UAChCtC,OAAO,CAACkC,GAAG,CAAC,qBAAqB,CAAC;QACpC,CAAC,MAAM;UACL;UACA;UACA,MAAMM,SAAS,CAACE,SAAS,CAACC,SAAS,CAACN,QAAQ,CAAC;UAC7CnI,KAAK,CAACgE,OAAO,CAAC,gCAAgC,CAAC;QACjD;MACF,CAAC,MAAM;QACLhE,KAAK,CAAC6F,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAIA,KAAK,CAACtB,IAAI,KAAK,YAAY,EAAE;QAC/BvE,KAAK,CAAC6F,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;EACF,CAAC;;EAED;EACA,MAAM6C,WAAW,GAAI7D,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAM8D,UAAU,GAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ,CAAC;IAExD,IAAIhE,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B,oBAAO7E,OAAA;QAAK4I,GAAG,EAAEjE,KAAK,CAACI,GAAI;QAAC9D,SAAS,EAAC,mBAAmB;QAAC4H,GAAG,EAAC,YAAY;QAAC3H,KAAK,EAAE;UAAC,GAAGuH,UAAU;UAAEK,SAAS,EAAE;QAAS;MAAE;QAAAzH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7H,CAAC,MAAM,IAAImD,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MACjC,oBACE7E,OAAA;QAAOiB,SAAS,EAAC,mBAAmB;QAAC8H,QAAQ;QAAC7H,KAAK,EAAEuH,UAAW;QAAAtH,QAAA,gBAC9DnB,OAAA;UAAQ4I,GAAG,EAAEjE,KAAK,CAACI,GAAI;UAACF,IAAI,EAAC;QAAW;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMwH,iBAAiB,GAAGA,CAACvE,OAAO,EAAEP,IAAI,KAAK;IAC3C,IAAI,CAACO,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAMwE,QAAQ,GAAG/E,IAAI,CAACS,KAAK,KAAKT,IAAI,CAACS,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIX,IAAI,CAACS,KAAK,CAACE,IAAI,KAAK,OAAO,CAAC;;IAE3F;IACA,IAAI,CAACoE,QAAQ,EAAE;MACb,oBACEjJ,OAAA;QAAAmB,QAAA,eACEnB,OAAA;UAAGiB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAEsD;QAAO;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAEV;;IAEA;IACA,MAAM0H,cAAc,GAAGzE,OAAO,CAACc,MAAM,GAAG,GAAG;IAC3C,MAAM4D,aAAa,GAAG3F,YAAY,CAACU,IAAI,CAACC,EAAE,CAAC;IAC3C,MAAMiF,WAAW,GAAGD,aAAa,GAAG1E,OAAO,GAAGA,OAAO,CAAC4E,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,cAAc,GAAG,KAAK,GAAG,EAAE,CAAC;IAEvG,oBACElJ,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QAAGiB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,GAC1BiI,WAAW,EACXF,cAAc,iBACblJ,OAAA;UACEiB,SAAS,EAAC,yDAAyD;UACnER,OAAO,EAAEA,CAAA,KAAMgD,eAAe,CAAC6B,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACpB,IAAI,CAACC,EAAE,GAAG,CAACgF;UAAc,CAAC,CAAC,CAAE;UAAAhI,QAAA,EAEhFgI,aAAa,GAAG,WAAW,GAAG;QAAW;UAAA9H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8H,mBAAmB,GAAGlK,WAAW,CAAC,CAACuH,MAAM,EAAE4C,KAAK,KAAK;IACzD5G,aAAa,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACqB,MAAM,GAAG4C;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAGpK,WAAW,CAAC,CAACqK,CAAC,EAAE9C,MAAM,KAAK;IACtD,IAAI8C,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBlC,mBAAmB,CAACb,MAAM,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgD,wBAAwB,GAAGvK,WAAW,CAAEuH,MAAM,IAAK;IACvDa,mBAAmB,CAACb,MAAM,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiD,cAAc,GAAI1F,IAAI,IAAK;IAC/B,IAAI,CAACtB,YAAY,CAACsB,IAAI,CAACC,EAAE,CAAC,EAAE,OAAO,IAAI;IAEvC,MAAM0C,QAAQ,GAAG/D,YAAY,CAACoB,IAAI,CAACC,EAAE,CAAC,IAAI,EAAE;IAC5C,MAAM0F,SAAS,GAAG7G,eAAe,CAACkB,IAAI,CAACC,EAAE,CAAC;IAC1C,MAAM2F,aAAa,GAAGxG,mBAAmB,CAACY,IAAI,CAACC,EAAE,CAAC;IAClD,MAAM/B,OAAO,GAAGgB,eAAe,CAACc,IAAI,CAACC,EAAE,CAAC;IAExC,oBACEnE,OAAA;MAAKiB,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCnB,OAAA;QAAIiB,SAAS,EAAC,MAAM;QAAAE,QAAA,GAAC,YAAU,EAAC+C,IAAI,CAACiB,aAAa,IAAI,CAAC,EAAC,GAAC;MAAA;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG9DxB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BnB,OAAA;UAAK4I,GAAG,EAAE,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgD,eAAe,KAAIjG,cAAe;UAAC0B,SAAS,EAAC,qBAAqB;UAAC4H,GAAG,EAAC,SAAS;UAAC3H,KAAK,EAAE;YAACwH,KAAK,EAAE,MAAM;YAAEqB,MAAM,EAAE;UAAM;QAAE;UAAA1I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClJxB,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BnB,OAAA;YACE6E,IAAI,EAAC,MAAM;YACX5D,SAAS,EAAC,cAAc;YACxB+I,WAAW,EAAC,oBAAoB;YAChCT,KAAK,EAAE7G,UAAU,CAACwB,IAAI,CAACC,EAAE,CAAC,IAAI,EAAG;YACjC8F,QAAQ,EAAGR,CAAC,IAAKH,mBAAmB,CAACpF,IAAI,CAACC,EAAE,EAAEsF,CAAC,CAACS,MAAM,CAACX,KAAK,CAAE;YAC9DY,SAAS,EAAGV,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAEvF,IAAI,CAACC,EAAE,CAAE;YACnDiG,SAAS,EAAE;UAAI;YAAA/I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFxB,OAAA;YAAKiB,SAAS,EAAC,iCAAiC;YAAAE,QAAA,eAC9CnB,OAAA;cAAOiB,SAAS,EAAE,CAACyB,UAAU,CAACwB,IAAI,CAACC,EAAE,CAAC,IAAI,EAAE,EAAEoB,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;cAAApE,QAAA,GACxF,CAACuB,UAAU,CAACwB,IAAI,CAACC,EAAE,CAAC,IAAI,EAAE,EAAEoB,MAAM,EAAC,iBACtC;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UACEiB,SAAS,EAAC,oCAAoC;UAC9CR,OAAO,EAAEA,CAAA,KAAMkJ,wBAAwB,CAACzF,IAAI,CAACC,EAAE,CAAE;UACjDkG,QAAQ,EAAE,CAAC3H,UAAU,CAACwB,IAAI,CAACC,EAAE,CAAC,IAAI,CAACzB,UAAU,CAACwB,IAAI,CAACC,EAAE,CAAC,CAACuD,IAAI,CAAC,CAAE;UAAAvG,QAAA,eAE9DnB,OAAA,CAACV,IAAI;YAACiB,IAAI,EAAC;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLqI,SAAS,gBACR7J,OAAA;QAAKiB,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BnB,OAAA;UAAKiB,SAAS,EAAC,kCAAkC;UAACqJ,IAAI,EAAC,QAAQ;UAAAnJ,QAAA,eAC7DnB,OAAA;YAAMiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNxB,OAAA;UAAGiB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,gBAENxB,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eAEEnB,OAAA;UAAKkB,KAAK,EAAE;YAAEyH,SAAS,EAAE,OAAO;YAAE4B,SAAS,EAAE;UAAO,CAAE;UAACpG,EAAE,EAAE,sBAAsBD,IAAI,CAACC,EAAE,EAAG;UAAAhD,QAAA,GAExF0F,QAAQ,CAAC5C,GAAG,CAAC6C,OAAO,iBACnB9G,OAAA;YAAsBiB,SAAS,EAAC,aAAa;YAAAE,QAAA,gBAC3CnB,OAAA;cAAK4I,GAAG,EAAE9B,OAAO,CAACvC,MAAO;cAACtD,SAAS,EAAC,qBAAqB;cAAC4H,GAAG,EAAE/B,OAAO,CAAC1C,IAAK;cAAClD,KAAK,EAAE;gBAACwH,KAAK,EAAE,MAAM;gBAAEqB,MAAM,EAAE;cAAM;YAAE;cAAA1I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvHxB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAAE,QAAA,gBAC/CnB,OAAA;gBAAKiB,SAAS,EAAC,SAAS;gBAAAE,QAAA,EAAE2F,OAAO,CAAC1C;cAAI;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CxB,OAAA;gBAAAmB,QAAA,EAAM2F,OAAO,CAACC;cAAI;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBxB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAE2F,OAAO,CAACE;cAAS;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GANEsF,OAAO,CAAC3C,EAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACN,CAAC,EAGDY,OAAO,iBACNpC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cACEiB,SAAS,EAAC,kDAAkD;cAC5DR,OAAO,EAAEA,CAAA,KAAM8G,gBAAgB,CAACrD,IAAI,CAACC,EAAE,CAAE;cACzCkG,QAAQ,EAAEP,aAAc;cAAA3I,QAAA,EAEvB2I,aAAa,gBACZ9J,OAAA,CAAAE,SAAA;gBAAAiB,QAAA,gBACEnB,OAAA;kBAAKiB,SAAS,EAAC,uCAAuC;kBAACqJ,IAAI,EAAC,QAAQ;kBAAAnJ,QAAA,eAClEnB,OAAA;oBAAMiB,SAAS,EAAC,iBAAiB;oBAAAE,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,4BAER;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,gBACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAID,oBACExB,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAE,QAAA,eAC7BnB,OAAA;MAAKiB,SAAS,EAAC,4BAA4B;MAAAE,QAAA,eACzCnB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAE,QAAA,gBAEbnB,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eACzCnB,OAAA;YAAKiB,SAAS,EAAC,sBAAsB;YAAAE,QAAA,eACnCnB,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAGrC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA;UAAKiB,SAAS,EAAC,wDAAwD;UAAAE,QAAA,gBACrEnB,OAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXxB,OAAA;YAAKiB,SAAS,EAAC,2BAA2B;YAAAE,QAAA,gBACxCnB,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5BnB,OAAA;gBAAIiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCxB,OAAA;gBAAOiB,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNxB,OAAA;cACE4I,GAAG,EAAE,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgD,eAAe,KAAIjG,cAAe;cACpD0B,SAAS,EAAC,gBAAgB;cAC1B4H,GAAG,EAAE,CAAArG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,IAAI,KAAI,SAAU;cACpCnD,KAAK,EAAE;gBAACwH,KAAK,EAAE,MAAM;gBAAEqB,MAAM,EAAE;cAAM;YAAE;cAAA1I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA,CAACR,QAAQ;UAACgL,YAAY,EAAE5C,gBAAiB;UAACpF,WAAW,EAAEA;QAAY;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAIrEc,cAAc,iBACbtC,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAACC,KAAK,EAAE;YAChCuJ,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE,oBAAoB;YAC5BnE,eAAe,EAAE;UACnB,CAAE;UAAApF,QAAA,eACAnB,OAAA;YAAKiB,SAAS,EAAC,4BAA4B;YAAAE,QAAA,gBACzCnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACqJ,IAAI,EAAC,QAAQ;cAAAnJ,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNxB,OAAA;cAAIiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxB,OAAA;cAAGiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAM,OAAO,gBACN9B,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAACqJ,IAAI,EAAC,QAAQ;YAAAnJ,QAAA,eAC3CnB,OAAA;cAAMiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNxB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,GACJI,KAAK,CAAC2D,MAAM,KAAK,CAAC,gBACpBvF,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA,CAACV,IAAI;YAACiB,IAAI,EAAC,kBAAkB;YAACW,KAAK,EAAE;cAAEE,QAAQ,EAAE,MAAM;cAAEuJ,KAAK,EAAE;YAAU;UAAE;YAAAtJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ExB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,gBAENxB,OAAA,CAAAE,SAAA;UAAAiB,QAAA,GAEGS,KAAK,CAACqC,GAAG,CAAC,CAACC,IAAI,EAAE0G,KAAK,kBACvB5K,OAAA;YAAmBiB,SAAS,EAAC,WAAW;YAAAE,QAAA,eACtCnB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAE,QAAA,gBAExBnB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA;kBAAK4I,GAAG,EAAE1E,IAAI,CAACE,IAAI,CAACG,MAAO;kBAACtD,SAAS,EAAC,qBAAqB;kBAAC4H,GAAG,EAAE3E,IAAI,CAACE,IAAI,CAACC,IAAK;kBAACnD,KAAK,EAAE;oBAACwH,KAAK,EAAE,MAAM;oBAAEqB,MAAM,EAAE;kBAAM;gBAAE;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3HxB,OAAA;kBAAKiB,SAAS,EAAC,aAAa;kBAAAE,QAAA,gBAC1BnB,OAAA;oBAAIiB,SAAS,EAAC,MAAM;oBAAAE,QAAA,EAAE+C,IAAI,CAACE,IAAI,CAACC;kBAAI;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CxB,OAAA;oBAAOiB,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE,IAAI8F,IAAI,CAAC/C,IAAI,CAACmB,UAAU,CAAC,CAAC8B,kBAAkB,CAAC;kBAAC;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,GAClB6H,iBAAiB,CAAC9E,IAAI,CAACO,OAAO,EAAEP,IAAI,CAAC,EACrCsE,WAAW,CAACtE,IAAI,CAACS,KAAK,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAE2D,IAAI,CAACxD,OAAO,GAAG,WAAW,GAAG,mBAAoB;kBACvDF,KAAK,EAAE0D,IAAI,CAACe,KAAM;kBAClBxE,OAAO,EAAEA,CAAA,KAAM2G,UAAU,CAAClD,IAAI,CAACC,EAAE,CAAE;kBACnCzD,OAAO,EAAEwD,IAAI,CAACxD,OAAQ;kBACtBE,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAE0D,IAAI,CAACiB,aAAa,IAAI,CAAE;kBAC/B1E,OAAO,EAAEA,CAAA,KAAM4G,aAAa,CAACnD,IAAI,CAACC,EAAE,CAAE;kBACtCvD,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,2BAA2B;kBAChCE,OAAO,EAAEA,CAAA,KAAMuH,WAAW,CAAC9D,IAAI,CAAE;kBACjCvD,MAAM,EAAE,IAAK;kBACbC,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLoI,cAAc,CAAC1F,IAAI,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC,GA7CE0C,IAAI,CAACC,EAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CZ,CACJ,CAAC,EAGDQ,WAAW,iBACVhC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/BnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACqJ,IAAI,EAAC,QAAQ;cAAAnJ,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNxB,OAAA;cAAGiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN,EAEA,CAACY,OAAO,IAAIR,KAAK,CAAC2D,MAAM,GAAG,CAAC,iBAC3BvF,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cAAGiB,SAAS,EAAC,YAAY;cAAAE,QAAA,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,GAAA,CAhmBID,MAAM;AAAAmJ,GAAA,GAANnJ,MAAM;AAkmBZ,eAAeA,MAAM;AAAC,IAAApB,EAAA,EAAAmB,GAAA,EAAAoJ,GAAA;AAAAC,YAAA,CAAAxK,EAAA;AAAAwK,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}