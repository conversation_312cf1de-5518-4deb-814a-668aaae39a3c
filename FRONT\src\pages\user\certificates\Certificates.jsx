import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import './Certificates.css';
import { getTraineeCertificates, GenerateCertificate } from '../../../services/userService';
import { toast } from 'react-toastify';
import NoData from '../../../components/common/NoData';

function UserCertificates() {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const [traineeCertificates, setTraineeCertificates] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchCertificates = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setIsRefreshing(true);
      }
      console.log('Fetching certificates...');
      const response = await getTraineeCertificates();
      console.log('Response:', response);
      if (response.success) {
        setTraineeCertificates(response.data.certificates);
        console.log('Certificates updated:', response.data.certificates.length, 'certificates found');
      } else {
        console.warn('Failed to fetch certificates:', response);
      }
    } catch (error) {
      console.error('Error fetching certificates:', error);
      toast.error('Error fetching certificates');
    } finally {
      if (showRefreshIndicator) {
        setIsRefreshing(false);
      }
    }
  };

  const handleGenerateCertificate = async () => {
    setIsGenerating(true);
    try {
      const response = await GenerateCertificate();
      if (response.success) {
        await fetchCertificates(true);
        // toast.success('Certificate generated successfully');
      } else {
        toast.error('Failed to generate certificate');
      }
      setIsGenerating(false);
    } catch (error) {
      setIsGenerating(false);
      console.error('Error generating certificate:', error);
      toast.info('No completed courses available for certificate');
      // Even if generation fails, refresh the list in case some certificates were created
      await fetchCertificates(true);
    }
  };

  useEffect(() => {
    fetchCertificates();
  }, []);

  const handleDownload = async (certificateUrl, certificateName) => {
    try {
      window.open(certificateUrl, '_blank');
    } catch (error) {
      console.error('Error downloading certificate:', error);
      toast.error(`Error downloading certificate: ${error.message}`);
    }
  };

  const openCertificateModal = (cert) => {
    setSelectedCertificate(cert);
    setShowDetailsModal(true);
  };

  const openCertificatePreview = (cert, e) => {
    e.stopPropagation();
    setSelectedCertificate(cert);
    setShowPreviewModal(true);
  };

  return (
    <>
      {/* Information message */}
      <div className="d-flex justify-content-end mb-2">
        <div className="col-12 col-md-6">
          <p className="text-muted text-end mb-0" style={{ fontSize: '13px' }}>
            After 100% course completion, click below
          </p>
        </div>
      </div>

      <div className="d-flex justify-content-end mb-3">
        <div className="col-12 col-md-3">
                  <button className='btn btn-primary' onClick={handleGenerateCertificate} disabled={isGenerating}>
          {isGenerating ? (
            <>
              <Spinner animation="border" size="sm" className="me-2" />
              Generating...
            </>
          ) : (
            <>
              <Icon icon="mdi:certificate" width="20" height="20" className="me-2" />
              Generate Certificate
            </>
          )}
        </button>

        </div>

      </div>

      {/* Refresh indicator */}
      {isRefreshing && (
        <div className="d-flex justify-content-center align-items-center mb-3">
          <Spinner animation="border" size="sm" className="me-2" />
          <span className="text-muted">Refreshing certificates...</span>
        </div>
      )}

      <div className="certificates-grid">
        {traineeCertificates.length > 0 ? (
          traineeCertificates.map((cert) => (
            <div key={cert.id} className="certificate-card">
              <div className="certificate-image">
                <img src={cert.certificate_url} alt={cert.certificate_name} />
                <div className="certificate-overlay">
                  <button 
                    className="preview-btn"
                    onClick={(e) => openCertificatePreview(cert, e)}
                  >
                    <Icon icon="mdi:eye-outline" width="24" height="24" />
                  </button>
                </div>
              </div>
              <div className="certificate-details">
                <h3>{cert.certificate_name}</h3>
                <div className="certificate-meta">
                  <span className="certificate-date">
                    <Icon icon="mdi:calendar-outline" width="16" height="16" />
                    Generated on: {new Date(cert.issued_date).toLocaleString()}
                  </span>
                </div>
                <div className="certificate-actions">
                  <button 
                    className="download-btn" 
                    onClick={() => handleDownload(cert.certificate_url, cert.certificate_name)}
                  >
                    <Icon icon="mdi:download" width="20" height="20" />
                    <span>Download</span>
                  </button>
                  <button 
                    className="view-details-btn"
                    onClick={() => openCertificateModal(cert)}
                  >
                    <Icon icon="mdi:information-outline" width="18" height="18" />
                    <span> Details</span>
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="no-data-container">
            <NoData message='No certificates found' />
          </div>
        )}
      </div>

      {/* Certificate Preview Modal */}
      <Modal
        show={showPreviewModal}
        onHide={() => setShowPreviewModal(false)}
        centered
        className="certificate-preview-modal"
        size="xl"
      >
        {selectedCertificate && (
          <>
            <Modal.Header closeButton>
              <Modal.Title>{selectedCertificate.certificate_name}</Modal.Title>
            </Modal.Header>
            <Modal.Body className="p-0">
              <div className="certificate-preview-container">
                <img 
                  src={selectedCertificate.certificate_url} 
                  alt={selectedCertificate.certificate_name}
                  className="preview-image"
                />
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button 
                variant="secondary" 
                onClick={() => setShowPreviewModal(false)}
              >
                Close
              </Button>
              <Button 
                variant="primary" 
                onClick={() => handleDownload(selectedCertificate.certificate_url, selectedCertificate.certificate_name)}
              >
                <Icon icon="mdi:download" width="18" height="18" className="me-2" />
                Download
              </Button>
            </Modal.Footer>
          </>
        )}
      </Modal>

      {/* Certificate Details Modal */}
      <Modal 
        show={showDetailsModal} 
        onHide={() => setShowDetailsModal(false)} 
        centered 
        className="certificate-details-modal"
        size="lg"
      >
        {selectedCertificate && (
          <>
            <Modal.Header closeButton>
              <Modal.Title>Certificate Information</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="certificate-form-layout">
                <div className="row mb-3">
                  <div className="col-12 mb-3">
                    <label className="form-label">Course Name</label>
                    <input type="text" className="form-control" value={selectedCertificate.course_name} readOnly />
                  </div>
                  <div className="col-12 mb-3">
                    <label className="form-label">Certificate Title</label>
                    <input type="text" className="form-control" value={selectedCertificate.certificate_name} readOnly />
                  </div>
                </div>
                
                <div className="row mb-3">
                  <div className="col-12 mb-3">
                    <label className="form-label">Trainee Name</label>
                    <input type="text" className="form-control" value={selectedCertificate.user_name} readOnly />
                  </div>
                  <div className="col-12 mb-3">
                    <label className="form-label">Trainee Email</label>
                    <input type="text" className="form-control" value={selectedCertificate.user_email} readOnly />
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-12 mb-3">
                    <label className="form-label">Created By</label>
                    <input type="text" className="form-control" value={selectedCertificate.createdBy} readOnly />
                  </div>
                  <div className="col-12">
                    <label className="form-label">Issue Date</label>
                    <input type="text" className="form-control" value={new Date(selectedCertificate.issued_date).toLocaleString()} readOnly />
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button 
                variant="secondary" 
                onClick={() => setShowDetailsModal(false)}
              >
                Close
              </Button>
              <Button 
                variant="primary" 
                onClick={() => handleDownload(selectedCertificate.certificate_url, selectedCertificate.certificate_name)}
              >
                <Icon icon="mdi:download" width="18" height="18" className="me-2" />
                Download Certificate
              </Button>
            </Modal.Footer>
          </>
        )}
      </Modal>
    </>
  );
}

export default UserCertificates;
