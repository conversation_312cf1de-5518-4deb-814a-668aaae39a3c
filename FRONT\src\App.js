import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useEffect, useRef } from 'react';

import AuthRoutes from './routes/AuthRoutes.jsx';
import UserRoutes from './routes/UserRoutes.jsx';
import AdminRoutes from './routes/AdminRoutes.jsx';
import PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';
import PublicPostDetails from './pages/public/PublicPostDetails.jsx';
import { NotificationProvider } from './context/NotificationContext';
import { PermissionsProvider } from './context/PermissionsContext';

import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import '../src/assets/styles/custom.css';
import './App.css';

function AppRouter() {
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');
  const navigate = useNavigate();
  const initialLoadRef = useRef(true);
  
  useEffect(() => {
    // Only redirect on initial load
    if (initialLoadRef.current && token && window.location.pathname === '/') {
      initialLoadRef.current = false;
      if (role === 'trainee') {
        navigate('/user/dashboard');
      } else {
        // For any other role (admin, trainer, etc.)
        navigate('/admin/dashboard');
      }
    }
  }, [token, role, navigate]);

  return (
    <Routes>
      {/* Public routes - accessible to everyone */}
      <Route path="/public/courseDetails/:encodedId" element={<PublicCourseDetails />} />
      <Route path="/public/postDetails/:encodedId" element={<PublicPostDetails />} />
      
      {/* Auth routes - when not logged in */}
      {!token && <Route path="/*" element={<AuthRoutes />} />}
      
      {/* User routes - when logged in as trainee */}
      {token && role === 'trainee' && <Route path="/*" element={<UserRoutes />} />}
      
      {/* Admin routes - when logged in as admin/trainer */}
      {token && role !== 'trainee' && <Route path="/*" element={<AdminRoutes />} />}
    </Routes>
  );
}

function App() {
  return (
    <PermissionsProvider>
    <NotificationProvider>
      <BrowserRouter>
        <AppRouter />
      </BrowserRouter>
    </NotificationProvider>
    </PermissionsProvider>
  );
}

export default App;
