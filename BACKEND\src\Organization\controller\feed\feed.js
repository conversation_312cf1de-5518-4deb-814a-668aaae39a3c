const { mysqlServerConnection } = require("../../../db/db");
const {
  uploadDocToS3,
  uploadVideoToS3,
  uploadImageToS3,
  uploadAudioToS3,
} = require("../../../tools/aws");
const { encodeData } = require("../../../utils/encodeAndEncode");
const mainDbName = process.env.MAIN_DB;

// Create a new feed post
const createPost = async (req, res) => {
  console.log('=== CREATE POST API CALLED ===');
  console.log('Request Body:', req.body);
  console.log('Request File:', req.file);
  console.log('User ID:', req.user.userId);
  console.log('Database Name:', req.user.db_name);

  try {
    const { description } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    console.log('Validating post content...');
    if (!description && !req.file) {
      console.log('ERROR: Post validation failed - no content provided');
      return res.status(400).json({
        success: false,
        data: { error_msg: "Post must contain either text or media" }
      });
    }

    let mediaUrl = "";
    let mediaType = "text";

    // Handle media upload
    if (req.file) {
      console.log('Processing file upload...');
      const fileType = req.file.mimetype.split('/')[0];
      console.log('File type detected:', fileType);

      if (fileType === 'image') {
        const uploadResult = await uploadImageToS3(req.file.buffer);
        mediaUrl = uploadResult.path;
        mediaType = 'image';
      } else if (fileType === 'video') {
        const uploadResult = await uploadVideoToS3(req.file.buffer);
        mediaUrl = uploadResult.path;
        mediaType = 'video';
      } else {
        return res.status(400).json({
          success: false,
          data: { error_msg: "Only image and video files are supported" }
        });
      }
    }

    console.log('Inserting post into database...');
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.feed_posts (user_id, media_url, media_type, description)
       VALUES (?, ?, ?, ?)`,
      [userId, mediaUrl, mediaType, description || ""]
    );

    const postId = result.insertId;
    console.log('Post inserted successfully with ID:', postId);

    // Encode post ID to generate shareable URL
    const encodedPostId = encodeData({ id: postId });

    const FRONTEND_URL = process.env.FRONTEND_URL || 'https://example.com';
    const shareUrl = `${FRONTEND_URL}/feed/post/${encodedPostId}`;
    console.log('Generated share URL:', shareUrl);

    // Update the post with the share URL
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.feed_posts SET share_url = ? WHERE id = ?`,
      [shareUrl, postId]
    );

    console.log('Post updated with share URL');

    console.log('=== CREATE POST API COMPLETED SUCCESSFULLY ===');

    return res.status(201).json({
      success: true,
      data: {
        message: "Post created successfully",
        post_id: postId,
        share_url: shareUrl
      }
    });

  } catch (error) {
    console.error("=== CREATE POST API ERROR ===");
    console.error("Error creating post:", error);
    console.error("Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};


// Edit an existing post
const editPost = async (req, res) => {
  console.log('=== EDIT POST API CALLED ===');
  console.log('Post ID:', req.params.postId);
  console.log('Request Body:', req.body);
  console.log('Request File:', req.file);
  console.log('User ID:', req.user.userId);
  console.log('Database Name:', req.user.db_name);
  
  try {
    const { postId } = req.params;
    const { description } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    // Check if post exists and belongs to user
    console.log('Checking if post exists and belongs to user...');
    const [existingPost] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.feed_posts WHERE id = ? AND user_id = ?`,
      [postId, userId]
    );
    
    console.log('Existing post check result:', existingPost.length > 0 ? 'Found' : 'Not found');

    if (existingPost.length === 0) {
      console.log('ERROR: Post not found or user does not have permission');
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found or you don't have permission to edit it" }
      });
    }

    let mediaUrl = existingPost[0].media_url;
    let mediaType = existingPost[0].media_type;

    // Handle new file upload if present
    if (req.file) {
      const fileType = req.file.mimetype.split('/')[0];

      if (fileType === 'image') {
        const uploadResult = await uploadImageToS3(req.file.buffer);
        mediaUrl = uploadResult.path;
        mediaType = 'image';
      } else if (fileType === 'video') {
        const uploadResult = await uploadVideoToS3(req.file.buffer);
        mediaUrl = uploadResult.path;
        mediaType = 'video';
      } else {
        return res.status(400).json({
          success: false,
          data: { error_msg: "Only image and video files are supported" }
        });
      }
    }

    // Update post
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.feed_posts
       SET description = ?, media_url = ?, media_type = ?
       WHERE id = ? AND user_id = ?`,
      [description || "", mediaUrl, mediaType, postId, userId]
    );

    // Get updated post with user details
    const [updatedPost] = await mysqlServerConnection.query(
      `SELECT fp.*, u.name as user_name, u.profile_pic_url as user_avatar
       FROM ${dbName}.feed_posts fp
       JOIN ${dbName}.users u ON fp.user_id = u.id
       WHERE fp.id = ?`,
      [postId]
    );

    return res.status(200).json({
      success: true,
      data: {
        message: "Post updated successfully",
        post: updatedPost[0]
      }
    });

  } catch (error) {
    console.error("Error editing post:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Delete a post
const deletePost = async (req, res) => {
  try {
    const { postId } = req.params;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    // Check if post exists and belongs to user
    const [existingPost] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.feed_posts WHERE id = ? AND user_id = ?`,
      [postId, userId]
    );

    if (existingPost.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found or you don't have permission to delete it" }
      });
    }

    // Delete related comments first
    await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.feed_comments WHERE feed_post_id = ?`,
      [postId]
    );

    // Delete related likes
    await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.feed_likes WHERE feed_post_id = ?`,
      [postId]
    );

    // Delete the post
    await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.feed_posts WHERE id = ? AND user_id = ?`,
      [postId, userId]
    );

    return res.status(200).json({
      success: true,
      data: { message: "Post deleted successfully" }
    });

  } catch (error) {
    console.error("Error deleting post:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

const getAllFeeds = async (req, res) => {
  console.log('=== GET ALL FEEDS API CALLED ===');
  console.log('Request Body:', req.body);
  console.log('User ID:', req.user.userId);
  console.log('Database Name:', req.user.db_name);

  try {
    const { page = 1, limit = 5, user_only = false } = req.body;
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    console.log('Query parameters:', { page, limit, user_only });

    const offset = (page - 1) * limit;
    console.log('Calculated offset:', offset);

    // Build WHERE clause for user-specific posts
    const whereClause = user_only ? `WHERE fp.user_id = ${userId}` : '';
    const countWhereClause = user_only ? `WHERE user_id = ${userId}` : '';
    console.log('WHERE clause:', whereClause);
    console.log('Count WHERE clause:', countWhereClause);

    // Fetch feed posts with user and interaction details
    console.log('Executing main query to fetch posts...');
    const [posts] = await mysqlServerConnection.query(
      `SELECT
        fp.*,
        u.name AS user_name,
        u.profile_pic_url AS user_avatar,
        COALESCE(like_count.total_likes, 0) AS likes_count,
        COALESCE(comment_count.total_comments, 0) AS comments_count,
        CASE WHEN user_likes.user_id IS NOT NULL THEN 1 ELSE 0 END AS is_liked_by_user
      FROM ${dbName}.feed_posts fp
      JOIN ${dbName}.users u ON fp.user_id = u.id
      LEFT JOIN (
        SELECT feed_post_id, COUNT(*) AS total_likes
        FROM ${dbName}.feed_likes
        GROUP BY feed_post_id
      ) like_count ON fp.id = like_count.feed_post_id
      LEFT JOIN (
        SELECT feed_post_id, COUNT(*) AS total_comments
        FROM ${dbName}.feed_comments
        GROUP BY feed_post_id
      ) comment_count ON fp.id = comment_count.feed_post_id
      LEFT JOIN ${dbName}.feed_likes user_likes
        ON fp.id = user_likes.feed_post_id AND user_likes.user_id = ?
      ${whereClause}
      ORDER BY fp.created_at DESC
      LIMIT ? OFFSET ?`,
      [userId, parseInt(limit), parseInt(offset)]
    );

    console.log('Posts fetched successfully. Count:', posts.length);

    // Get total number of posts for pagination
    console.log('Getting total post count for pagination...');
    const [totalCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS total FROM ${dbName}.feed_posts ${countWhereClause}`
    );

    const totalPosts = totalCount[0].total;
    const totalPages = Math.ceil(totalPosts / limit);
    const hasMore = page < totalPages;

    console.log('Pagination info:', {
      totalPosts,
      totalPages,
      currentPage: page,
      hasMore
    });

    // Fetch the logged-in user's profile
    console.log('Fetching user profile...');
    const [userProfileResult] = await mysqlServerConnection.query(
      `SELECT id, name, email, profile_pic_url, bio FROM ${dbName}.users WHERE id = ?`,
      [userId]
    );
    const userProfile = userProfileResult[0] || {};

    console.log('User profile fetched:', userProfile);

    // Final Response
    console.log('=== GET ALL FEEDS API COMPLETED SUCCESSFULLY ===');
    return res.status(200).json({
      success: true,
      data: {
        posts,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_posts: totalPosts,
          has_more: hasMore,
          limit: parseInt(limit)
        },
        user_profile: {
          id: userProfile.id,
          name: userProfile.name,
          email: userProfile.email,
          profile_pic_url: userProfile.profile_pic_url,
          bio: userProfile.bio
        }
      }
    });

  } catch (error) {
    console.error('=== GET ALL FEEDS API ERROR ===');
    console.error('Error fetching feeds:', error);
    console.error('Error stack:', error.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error' }
    });
  }
};


// Like or unlike a post
const toggleLike = async (req, res) => {
  console.log('=== TOGGLE LIKE API CALLED ===');
  console.log('Post ID:', req.params.postId);
  console.log('User ID:', req.user.userId);
  console.log('Database Name:', req.user.db_name);
  
  try {
    const { postId } = req.params;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    // Check if post exists
    const [postExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.feed_posts WHERE id = ?`,
      [postId]
    );

    if (postExists.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found" }
      });
    }

    // Check if user already liked the post
    const [existingLike] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.feed_likes WHERE feed_post_id = ? AND user_id = ?`,
      [postId, userId]
    );

    let isLiked = false;
    let message = "";

    if (existingLike.length > 0) {
      // Unlike the post
      await mysqlServerConnection.query(
        `DELETE FROM ${dbName}.feed_likes WHERE feed_post_id = ? AND user_id = ?`,
        [postId, userId]
      );
      message = "Post unliked successfully";
      isLiked = false;
    } else {
      // Like the post
      await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.feed_likes (feed_post_id, user_id) VALUES (?, ?)`,
        [postId, userId]
      );
      message = "Post liked successfully";
      isLiked = true;
    }

    // Get updated like count
    const [likeCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total_likes FROM ${dbName}.feed_likes WHERE feed_post_id = ?`,
      [postId]
    );

    return res.status(200).json({
      success: true,
      data: {
        message,
        is_liked: isLiked,
        likes_count: likeCount[0].total_likes
      }
    });

  } catch (error) {
    console.error("=== TOGGLE LIKE API ERROR ===");
    console.error("Error toggling like:", error);
    console.error("Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Add a comment to a post
const addComment = async (req, res) => {
  console.log('=== ADD COMMENT API CALLED ===');
  console.log('Post ID:', req.params.postId);
  console.log('Request Body:', req.body);
  console.log('User ID:', req.user.userId);
  console.log('Database Name:', req.user.db_name);
  
  try {
    const { postId } = req.params;
    const { comment } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    if (!comment || comment.trim() === "") {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Comment cannot be empty" }
      });
    }

    // Check if post exists
    const [postExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.feed_posts WHERE id = ?`,
      [postId]
    );

    if (postExists.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found" }
      });
    }

    // Insert comment
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.feed_comments (feed_post_id, user_id, comment) VALUES (?, ?, ?)`,
      [postId, userId, comment.trim()]
    );

    // Get the created comment with user details
    const [commentData] = await mysqlServerConnection.query(
      `SELECT fc.*, u.name as user_name, u.profile_pic_url as user_avatar
       FROM ${dbName}.feed_comments fc
       JOIN ${dbName}.users u ON fc.user_id = u.id
       WHERE fc.id = ?`,
      [result.insertId]
    );

    return res.status(201).json({
      success: true,
      data: {
        message: "Comment added successfully",
        comment: commentData[0]
      }
    });

  } catch (error) {
    console.error("=== ADD COMMENT API ERROR ===");
    console.error("Error adding comment:", error);
    console.error("Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Edit a comment
const editComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const { comment } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    if (!comment || comment.trim() === "") {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Comment cannot be empty" }
      });
    }

    // Check if comment exists and belongs to user
    const [existingComment] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.feed_comments WHERE id = ? AND user_id = ?`,
      [commentId, userId]
    );

    if (existingComment.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Comment not found or you don't have permission to edit it" }
      });
    }

    // Update comment
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.feed_comments SET comment = ? WHERE id = ? AND user_id = ?`,
      [comment.trim(), commentId, userId]
    );

    // Get updated comment with user details
    const [updatedComment] = await mysqlServerConnection.query(
      `SELECT fc.*, u.name as user_name, u.profile_pic_url as user_avatar
       FROM ${dbName}.feed_comments fc
       JOIN ${dbName}.users u ON fc.user_id = u.id
       WHERE fc.id = ?`,
      [commentId]
    );

    return res.status(200).json({
      success: true,
      data: {
        message: "Comment updated successfully",
        comment: updatedComment[0]
      }
    });

  } catch (error) {
    console.error("Error editing comment:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Delete a comment
const deleteComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    // Check if comment exists and belongs to user
    const [existingComment] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.feed_comments WHERE id = ? AND user_id = ?`,
      [commentId, userId]
    );

    if (existingComment.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Comment not found or you don't have permission to delete it" }
      });
    }

    // Delete comment
    await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.feed_comments WHERE id = ? AND user_id = ?`,
      [commentId, userId]
    );

    return res.status(200).json({
      success: true,
      data: { message: "Comment deleted successfully" }
    });

  } catch (error) {
    console.error("Error deleting comment:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Get comments for a specific post
const getPostComments = async (req, res) => {
  console.log('=== GET POST COMMENTS API CALLED ===');
  console.log('Post ID:', req.params.postId);
  console.log('Query parameters:', req.query);
  console.log('Database Name:', req.user.db_name);
  
  try {
    const { postId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const dbName = req.user.db_name;
    
    console.log('Processed parameters:', { postId, page, limit });

    const offset = (page - 1) * limit;
    console.log('Calculated offset:', offset);

    // Check if post exists
    console.log('Checking if post exists...');
    const [postExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.feed_posts WHERE id = ?`,
      [postId]
    );

    if (postExists.length === 0) {
      console.log('ERROR: Post not found');
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found" }
      });
    }
    
    console.log('Post exists, fetching comments...');

    // Get comments with user details
    console.log('Executing query to fetch comments...');
    const [comments] = await mysqlServerConnection.query(
      `SELECT fc.*, u.name as user_name, u.profile_pic_url as user_avatar
       FROM ${dbName}.feed_comments fc
       JOIN ${dbName}.users u ON fc.user_id = u.id
       WHERE fc.feed_post_id = ?
       ORDER BY fc.commented_at DESC
       LIMIT ? OFFSET ?`,
      [postId, parseInt(limit), parseInt(offset)]
    );
    
    console.log('Comments fetched successfully. Count:', comments.length);
    console.log('First comment sample:', comments[0] ? {
      id: comments[0].id,
      user_name: comments[0].user_name,
      comment: comments[0].comment.substring(0, 50) + '...'
    } : 'No comments found');

    // Get total comment count
    console.log('Getting total comment count...');
    const [totalCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total FROM ${dbName}.feed_comments WHERE feed_post_id = ?`,
      [postId]
    );

    const totalComments = totalCount[0].total;
    const totalPages = Math.ceil(totalComments / limit);
    const hasMore = page < totalPages;
    
    console.log('Comment pagination info:', {
      totalComments,
      totalPages,
      currentPage: page,
      hasMore
    });

    console.log('=== GET POST COMMENTS API COMPLETED SUCCESSFULLY ===');
    return res.status(200).json({
      success: true,
      data: {
        comments,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_comments: totalComments,
          has_more: hasMore,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error("=== GET POST COMMENTS API ERROR ===");
    console.error("Error fetching comments:", error);
    console.error("Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error" }
    });
  }
};

// Generate shareable URL for a post
const generatePostShareUrl = async (req, res) => {
  console.log('=== GENERATE POST SHARE URL API CALLED ===');
  console.log('Request Body:', req.body);
  console.log('User ID:', req.user.userId);

  try {
    const { post_id } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    if (!post_id) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Post ID is required" }
      });
    }

    console.log('Generating share URL for post ID:', post_id);

    // Verify the post exists and belongs to the user or is public
    const [postRows] = await mysqlServerConnection.query(`
      SELECT id, user_id FROM ${dbName}.feed_posts WHERE id = ?
    `, [post_id]);

    if (postRows.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found" }
      });
    }

    const post = postRows[0];

    // Check if user owns the post (for now, only allow owners to generate share URLs)
    if (post.user_id !== userId) {
      return res.status(403).json({
        success: false,
        data: { error_msg: "You can only generate share URLs for your own posts" }
      });
    }

    // Get domain from request or use default
    const domain = req.body.domain || req.get('host') || 'localhost:3001';
    console.log('Using domain for post URL:', domain);

    // Create the post data object to encode
    const postDataToEncode = { id: parseInt(post_id) };
    const encodedPostData = encodeData(postDataToEncode);

    // Generate the public post URL
    const publicPostUrl = `/public/postDetails/${encodedPostData}`;

    console.log("Generated public post URL:", publicPostUrl);

    res.status(200).json({
      success: true,
      data: {
        postId: post_id,
        shareUrl: publicPostUrl,
        encodedData: encodedPostData,
        domain: domain
      }
    });

  } catch (error) {
    console.error('Error generating post share URL:', error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error: " + error.message }
    });
  }
};

// Get public post details (no authentication required)
const getPublicPostDetails = async (req, res) => {
  console.log('=== GET PUBLIC POST DETAILS API CALLED ===');
  console.log('🟡 Request Body:', req.body);

  try {
    const { post_id, domain } = req.body;

    console.log('🔍 Validating input...');
    if (!post_id || !domain) {
      console.log('❌ Validation failed: Missing post_id or domain');
      return res.status(400).json({
        success: false,
        data: { error_msg: "Post ID and domain are required" }
      });
    }

    console.log('🌐 Fetching DB name for domain:', domain);
    const [orgResult] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${mainDbName}.organization WHERE auth_sub_domain = ?`,
      [domain]
    );

    if (orgResult.length === 0) {
      console.log('❌ No organization found for domain:', domain);
      return res.status(404).json({
        success: false,
        data: { error_msg: "Organization not found" }
      });
    }

    const dbname = orgResult[0].db_name;
    console.log('✅ Organization DB resolved:', dbname);

    console.log('📄 Fetching post details for post ID:', post_id);
    const [postRows] = await mysqlServerConnection.query(`
      SELECT
        fp.id,
        fp.description,
        fp.media_url,
        fp.media_type,
        fp.created_at,
        u.name AS user_name,
        u.profile_pic_url AS user_avatar,
        (SELECT COUNT(*) FROM ${dbname}.feed_likes fl WHERE fl.feed_post_id = fp.id) AS likes_count,
        (SELECT COUNT(*) FROM ${dbname}.feed_comments fc WHERE fc.feed_post_id = fp.id) AS comments_count
      FROM ${dbname}.feed_posts fp
      LEFT JOIN ${dbname}.users u ON fp.user_id = u.id
      WHERE fp.id = ?
    `, [post_id]);

    if (postRows.length === 0) {
      console.log('❌ No post found with ID:', post_id);
      return res.status(404).json({
        success: false,
        data: { error_msg: "Post not found" }
      });
    }

    const post = postRows[0];
    console.log('✅ Public post retrieved successfully');
    console.log('📝 Post Summary:', {
      id: post.id,
      user: post.user_name,
      media_type: post.media_type,
      likes: post.likes_count,
      comments: post.comments_count
    });

    return res.status(200).json({
      success: true,
      data: {
        message: "Public post details fetched successfully",
        post: post
      }
    });

  } catch (error) {
    console.error('🔥 Error fetching public post details:', error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error: " + error.message }
    });
  }
};


module.exports = {
  createPost,
  editPost,
  deletePost,
  getAllFeeds,
  toggleLike,
  addComment,
  editComment,
  deleteComment,
  getPostComments,
  generatePostShareUrl,
  getPublicPostDetails
};