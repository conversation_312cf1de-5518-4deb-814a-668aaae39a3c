{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\FeedPost.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport { createPost } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move MediaUploadButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MediaUploadButton = /*#__PURE__*/React.memo(_c = ({\n  icon,\n  text,\n  onClick,\n  buttonStyle\n}) => /*#__PURE__*/_jsxDEV(\"button\", {\n  className: \"btn border text-muted btn-sm\",\n  style: buttonStyle,\n  onClick: onClick,\n  type: \"button\",\n  children: [/*#__PURE__*/_jsxDEV(Icon, {\n    icon: icon,\n    className: \"me-1 d-none d-md-inline\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Icon, {\n    icon: icon,\n    className: \"d-md-none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n    className: \"d-none d-md-inline\",\n    children: text\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this));\n_c2 = MediaUploadButton;\nconst FeedPost = ({\n  onPostSubmit,\n  userProfile\n}) => {\n  _s();\n  const [newPost, setNewPost] = useState('');\n  const [newPostMedia, setNewPostMedia] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const MAX_CHARACTERS = 1000;\n\n  // Refs for file inputs\n  const imageInputRef = useRef(null);\n  const videoInputRef = useRef(null);\n\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const postButtonStyle = useMemo(() => ({\n    borderRadius: '20px',\n    fontWeight: '500',\n    transition: 'all 0.2s ease',\n    minWidth: '100px'\n  }), []);\n\n  // Event handlers\n  const handleMediaUpload = useCallback((e, type) => {\n    const file = e.target.files[0];\n    if (file) {\n      setNewPostMedia({\n        type,\n        url: URL.createObjectURL(file),\n        file\n      });\n    }\n  }, []);\n  const handleImageUpload = useCallback(e => {\n    handleMediaUpload(e, 'image');\n  }, [handleMediaUpload]);\n  const handleVideoUpload = useCallback(e => {\n    handleMediaUpload(e, 'video');\n  }, [handleMediaUpload]);\n  const handleImageButtonClick = useCallback(() => {\n    var _imageInputRef$curren;\n    (_imageInputRef$curren = imageInputRef.current) === null || _imageInputRef$curren === void 0 ? void 0 : _imageInputRef$curren.click();\n  }, []);\n  const handleVideoButtonClick = useCallback(() => {\n    var _videoInputRef$curren;\n    (_videoInputRef$curren = videoInputRef.current) === null || _videoInputRef$curren === void 0 ? void 0 : _videoInputRef$curren.click();\n  }, []);\n  const handleSubmitPost = async () => {\n    if (!newPost.trim() && !newPostMedia) {\n      toast.error('Please add some content or media to your post');\n      return;\n    }\n    if (newPost.length > MAX_CHARACTERS) {\n      toast.error(`Post content cannot exceed ${MAX_CHARACTERS} characters`);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      const postData = {\n        description: newPost.trim(),\n        media: newPostMedia\n      };\n      const response = await createPost(postData);\n      if (response.success) {\n        toast.success('Post created successfully!');\n        setNewPost('');\n        setNewPostMedia(null);\n\n        // Call the parent callback to refresh the feed\n        if (onPostSubmit) {\n          onPostSubmit(response.data.post);\n        }\n      } else {\n        var _response$data;\n        toast.error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error_msg) || 'Failed to create post');\n      }\n    } catch (error) {\n      console.error('Error creating post:', error);\n      toast.error('Failed to create post. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    const mediaStyle = {\n      width: '100%',\n      maxHeight: '400px'\n    };\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"img\", {\n        src: media.url,\n        className: \"img-fluid rounded\",\n        alt: \"Post media\",\n        style: {\n          ...mediaStyle,\n          objectFit: 'cover'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 14\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"video\", {\n        className: \"img-fluid rounded\",\n        controls: true,\n        style: mediaStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: media.url,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const MediaUploadButton = ({\n    icon,\n    text,\n    onClick\n  }) => /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"btn border text-muted btn-sm\",\n    style: buttonStyle,\n    onClick: onClick,\n    type: \"button\",\n    children: [/*#__PURE__*/_jsxDEV(Icon, {\n      icon: icon,\n      className: \"me-1 d-none d-md-inline\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Icon, {\n      icon: icon,\n      className: \"d-md-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"d-none d-md-inline\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card mb-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n          className: \"rounded-circle me-3\",\n          alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"Profile\",\n          style: {\n            width: '40px',\n            height: '40px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"form-control border-0\",\n            rows: \"3\",\n            placeholder: \"What's on your mind?\",\n            value: newPost,\n            onChange: e => setNewPost(e.target.value),\n            maxLength: MAX_CHARACTERS\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: `${newPost.length > MAX_CHARACTERS * 0.9 ? 'text-warning' : 'text-muted'}`,\n              children: [newPost.length, \"/\", MAX_CHARACTERS, \" characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), newPostMedia && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative\",\n          children: [renderMedia(newPostMedia), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-danger position-absolute top-0 end-0 m-2 w-auto\",\n            onClick: () => setNewPostMedia(null),\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: imageInputRef,\n        type: \"file\",\n        accept: \"image/*\",\n        className: \"d-none\",\n        onChange: handleImageUpload\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: videoInputRef,\n        type: \"file\",\n        accept: \"video/*\",\n        className: \"d-none\",\n        onChange: handleVideoUpload\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(MediaUploadButton, {\n            icon: \"mdi:camera\",\n            text: \"Photo\",\n            onClick: handleImageButtonClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MediaUploadButton, {\n            icon: \"mdi:video\",\n            text: \"Video\",\n            onClick: handleVideoButtonClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `btn px-4 w-auto py-2 ${(newPost.trim() || newPostMedia) && !isSubmitting ? 'btn-primary' : 'btn-secondary'}`,\n          onClick: handleSubmitPost,\n          disabled: !newPost.trim() && !newPostMedia || isSubmitting,\n          style: postButtonStyle,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), \"Posting...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:send\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), \"Post\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(FeedPost, \"cDeiXPHES7NKbDrmIQVsMGTyJSY=\");\n_c3 = FeedPost;\nexport default FeedPost;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MediaUploadButton$React.memo\");\n$RefreshReg$(_c2, \"MediaUploadButton\");\n$RefreshReg$(_c3, \"FeedPost\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useMemo", "Icon", "DefaultProfile", "createPost", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MediaUploadButton", "memo", "_c", "icon", "text", "onClick", "buttonStyle", "className", "style", "type", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "FeedPost", "onPostSubmit", "userProfile", "_s", "newPost", "setNewPost", "newPostMedia", "setNewPostMedia", "isSubmitting", "setIsSubmitting", "MAX_CHARACTERS", "imageInputRef", "videoInputRef", "backgroundColor", "borderColor", "postButtonStyle", "borderRadius", "fontWeight", "transition", "min<PERSON><PERSON><PERSON>", "handleMediaUpload", "e", "file", "target", "files", "url", "URL", "createObjectURL", "handleImageUpload", "handleVideoUpload", "handleImageButtonClick", "_imageInputRef$curren", "current", "click", "handleVideoButtonClick", "_videoInputRef$curren", "handleSubmitPost", "trim", "error", "length", "postData", "description", "media", "response", "success", "data", "post", "_response$data", "error_msg", "console", "renderMedia", "mediaStyle", "width", "maxHeight", "src", "alt", "objectFit", "controls", "profile_pic_url", "name", "height", "rows", "placeholder", "value", "onChange", "max<PERSON><PERSON><PERSON>", "ref", "accept", "disabled", "role", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/FeedPost.jsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useMemo } from 'react'\r\nimport { Icon } from '@iconify/react'\r\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\r\nimport { createPost } from '../../../services/feedServices'\r\nimport { toast } from 'react-toastify'\r\n\r\n// Move MediaUploadButton outside to prevent recreation on every render\r\nconst MediaUploadButton = React.memo(({ icon, text, onClick, buttonStyle }) => (\r\n  <button className=\"btn border text-muted btn-sm\" style={buttonStyle} onClick={onClick} type=\"button\">\r\n    <Icon icon={icon} className=\"me-1 d-none d-md-inline\" />\r\n    <Icon icon={icon} className=\"d-md-none\" />\r\n    <span className=\"d-none d-md-inline\">{text}</span>\r\n  </button>\r\n));\r\n\r\nconst FeedPost = ({ onPostSubmit, userProfile }) => {\r\n  const [newPost, setNewPost] = useState('');\r\n  const [newPostMedia, setNewPostMedia] = useState(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const MAX_CHARACTERS = 1000;\r\n\r\n  // Refs for file inputs\r\n  const imageInputRef = useRef(null);\r\n  const videoInputRef = useRef(null);\r\n\r\n  // Memoized styles to prevent re-renders\r\n  const buttonStyle = useMemo(() => ({\r\n    backgroundColor: 'transparent',\r\n    borderColor: '#dee2e6'\r\n  }), []);\r\n\r\n  const postButtonStyle = useMemo(() => ({\r\n    borderRadius: '20px',\r\n    fontWeight: '500',\r\n    transition: 'all 0.2s ease',\r\n    minWidth: '100px'\r\n  }), []);\r\n\r\n  // Event handlers\r\n  const handleMediaUpload = useCallback((e, type) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setNewPostMedia({\r\n        type,\r\n        url: URL.createObjectURL(file),\r\n        file\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  const handleImageUpload = useCallback((e) => {\r\n    handleMediaUpload(e, 'image');\r\n  }, [handleMediaUpload]);\r\n\r\n  const handleVideoUpload = useCallback((e) => {\r\n    handleMediaUpload(e, 'video');\r\n  }, [handleMediaUpload]);\r\n\r\n  const handleImageButtonClick = useCallback(() => {\r\n    imageInputRef.current?.click();\r\n  }, []);\r\n\r\n  const handleVideoButtonClick = useCallback(() => {\r\n    videoInputRef.current?.click();\r\n  }, []);\r\n\r\n  const handleSubmitPost = async () => {\r\n    if (!newPost.trim() && !newPostMedia) {\r\n      toast.error('Please add some content or media to your post');\r\n      return;\r\n    }\r\n\r\n    if (newPost.length > MAX_CHARACTERS) {\r\n      toast.error(`Post content cannot exceed ${MAX_CHARACTERS} characters`);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const postData = {\r\n        description: newPost.trim(),\r\n        media: newPostMedia\r\n      };\r\n\r\n      const response = await createPost(postData);\r\n\r\n      if (response.success) {\r\n        toast.success('Post created successfully!');\r\n        setNewPost('');\r\n        setNewPostMedia(null);\r\n\r\n        // Call the parent callback to refresh the feed\r\n        if (onPostSubmit) {\r\n          onPostSubmit(response.data.post);\r\n        }\r\n      } else {\r\n        toast.error(response.data?.error_msg || 'Failed to create post');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating post:', error);\r\n      toast.error('Failed to create post. Please try again.');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Render functions\r\n  const renderMedia = (media) => {\r\n    if (!media) return null;\r\n\r\n    const mediaStyle = { width: '100%', maxHeight: '400px' };\r\n\r\n    if (media.type === 'image') {\r\n      return <img src={media.url} className=\"img-fluid rounded\" alt=\"Post media\" style={{...mediaStyle, objectFit: 'cover'}} />;\r\n    } else if (media.type === 'video') {\r\n      return (\r\n        <video className=\"img-fluid rounded\" controls style={mediaStyle}>\r\n          <source src={media.url} type=\"video/mp4\" />\r\n          Your browser does not support the video tag.\r\n        </video>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const MediaUploadButton = ({ icon, text, onClick }) => (\r\n    <button className=\"btn border text-muted btn-sm\" style={buttonStyle} onClick={onClick} type=\"button\">\r\n      <Icon icon={icon} className=\"me-1 d-none d-md-inline\" />\r\n      <Icon icon={icon} className=\"d-md-none\" />\r\n      <span className=\"d-none d-md-inline\">{text}</span>\r\n    </button>\r\n  );\r\n\r\n  return (\r\n    <div className=\"card mb-4\">\r\n      <div className=\"card-body\">\r\n        <div className=\"d-flex mb-3\">\r\n          <img \r\n            src={userProfile?.profile_pic_url || DefaultProfile} \r\n            className=\"rounded-circle me-3\" \r\n            alt={userProfile?.name || \"Profile\"} \r\n            style={{width: '40px', height: '40px'}} \r\n          />\r\n          <div className=\"flex-grow-1\">\r\n            <textarea \r\n              className=\"form-control border-0\" \r\n              rows=\"3\" \r\n              placeholder=\"What's on your mind?\"\r\n              value={newPost}\r\n              onChange={(e) => setNewPost(e.target.value)}\r\n              maxLength={MAX_CHARACTERS}\r\n            />\r\n            <div className=\"d-flex justify-content-end mt-2\">\r\n              <small className={`${newPost.length > MAX_CHARACTERS * 0.9 ? 'text-warning' : 'text-muted'}`}>\r\n                {newPost.length}/{MAX_CHARACTERS} characters\r\n              </small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Media Preview */}\r\n        {newPostMedia && (\r\n          <div className=\"mb-3\">\r\n            <div className=\"position-relative\">\r\n              {renderMedia(newPostMedia)}\r\n              <button \r\n                className=\"btn btn-sm btn-danger position-absolute top-0 end-0 m-2 w-auto\"\r\n                onClick={() => setNewPostMedia(null)}\r\n              >\r\n                <Icon icon=\"mdi:close\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Hidden file inputs */}\r\n        <input\r\n          ref={imageInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          className=\"d-none\"\r\n          onChange={handleImageUpload}\r\n        />\r\n        <input\r\n          ref={videoInputRef}\r\n          type=\"file\"\r\n          accept=\"video/*\"\r\n          className=\"d-none\"\r\n          onChange={handleVideoUpload}\r\n        />\r\n\r\n        {/* Action Buttons */}\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div className=\"d-flex gap-2\">\r\n            <MediaUploadButton icon=\"mdi:camera\" text=\"Photo\" onClick={handleImageButtonClick} />\r\n            <MediaUploadButton icon=\"mdi:video\" text=\"Video\" onClick={handleVideoButtonClick} />\r\n          </div>\r\n          <button\r\n            className={`btn px-4 w-auto py-2 ${(newPost.trim() || newPostMedia) && !isSubmitting ? 'btn-primary' : 'btn-secondary'}`}\r\n            onClick={handleSubmitPost}\r\n            disabled={(!newPost.trim() && !newPostMedia) || isSubmitting}\r\n            style={postButtonStyle}\r\n          >\r\n            {isSubmitting ? (\r\n              <>\r\n                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n                Posting...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Icon icon=\"mdi:send\" className=\"me-2\" />\r\n                Post\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeedPost;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACrE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,gBAAGb,KAAK,CAACc,IAAI,CAAAC,EAAA,GAACA,CAAC;EAAEC,IAAI;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAY,CAAC,kBACxET,OAAA;EAAQU,SAAS,EAAC,8BAA8B;EAACC,KAAK,EAAEF,WAAY;EAACD,OAAO,EAAEA,OAAQ;EAACI,IAAI,EAAC,QAAQ;EAAAC,QAAA,gBAClGb,OAAA,CAACL,IAAI;IAACW,IAAI,EAAEA,IAAK;IAACI,SAAS,EAAC;EAAyB;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACxDjB,OAAA,CAACL,IAAI;IAACW,IAAI,EAAEA,IAAK;IAACI,SAAS,EAAC;EAAW;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC1CjB,OAAA;IAAMU,SAAS,EAAC,oBAAoB;IAAAG,QAAA,EAAEN;EAAI;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5C,CACT,CAAC;AAACC,GAAA,GANGf,iBAAiB;AAQvB,MAAMgB,QAAQ,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMsC,cAAc,GAAG,IAAI;;EAE3B;EACA,MAAMC,aAAa,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuC,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAMiB,WAAW,GAAGf,OAAO,CAAC,OAAO;IACjCsC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMC,eAAe,GAAGxC,OAAO,CAAC,OAAO;IACrCyC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,EAAE,CAAC;;EAEP;EACA,MAAMC,iBAAiB,GAAG9C,WAAW,CAAC,CAAC+C,CAAC,EAAE5B,IAAI,KAAK;IACjD,MAAM6B,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRf,eAAe,CAAC;QACdd,IAAI;QACJgC,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QAC9BA;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,iBAAiB,GAAGtD,WAAW,CAAE+C,CAAC,IAAK;IAC3CD,iBAAiB,CAACC,CAAC,EAAE,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMS,iBAAiB,GAAGvD,WAAW,CAAE+C,CAAC,IAAK;IAC3CD,iBAAiB,CAACC,CAAC,EAAE,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMU,sBAAsB,GAAGxD,WAAW,CAAC,MAAM;IAAA,IAAAyD,qBAAA;IAC/C,CAAAA,qBAAA,GAAApB,aAAa,CAACqB,OAAO,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBE,KAAK,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,sBAAsB,GAAG5D,WAAW,CAAC,MAAM;IAAA,IAAA6D,qBAAA;IAC/C,CAAAA,qBAAA,GAAAvB,aAAa,CAACoB,OAAO,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,KAAK,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAChC,OAAO,CAACiC,IAAI,CAAC,CAAC,IAAI,CAAC/B,YAAY,EAAE;MACpC3B,KAAK,CAAC2D,KAAK,CAAC,+CAA+C,CAAC;MAC5D;IACF;IAEA,IAAIlC,OAAO,CAACmC,MAAM,GAAG7B,cAAc,EAAE;MACnC/B,KAAK,CAAC2D,KAAK,CAAC,8BAA8B5B,cAAc,aAAa,CAAC;MACtE;IACF;IAEAD,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM+B,QAAQ,GAAG;QACfC,WAAW,EAAErC,OAAO,CAACiC,IAAI,CAAC,CAAC;QAC3BK,KAAK,EAAEpC;MACT,CAAC;MAED,MAAMqC,QAAQ,GAAG,MAAMjE,UAAU,CAAC8D,QAAQ,CAAC;MAE3C,IAAIG,QAAQ,CAACC,OAAO,EAAE;QACpBjE,KAAK,CAACiE,OAAO,CAAC,4BAA4B,CAAC;QAC3CvC,UAAU,CAAC,EAAE,CAAC;QACdE,eAAe,CAAC,IAAI,CAAC;;QAErB;QACA,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAAC0C,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC;QAClC;MACF,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLpE,KAAK,CAAC2D,KAAK,CAAC,EAAAS,cAAA,GAAAJ,QAAQ,CAACE,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAeC,SAAS,KAAI,uBAAuB,CAAC;MAClE;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C3D,KAAK,CAAC2D,KAAK,CAAC,0CAA0C,CAAC;IACzD,CAAC,SAAS;MACR7B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyC,WAAW,GAAIR,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMS,UAAU,GAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ,CAAC;IAExD,IAAIX,KAAK,CAACjD,IAAI,KAAK,OAAO,EAAE;MAC1B,oBAAOZ,OAAA;QAAKyE,GAAG,EAAEZ,KAAK,CAACjB,GAAI;QAAClC,SAAS,EAAC,mBAAmB;QAACgE,GAAG,EAAC,YAAY;QAAC/D,KAAK,EAAE;UAAC,GAAG2D,UAAU;UAAEK,SAAS,EAAE;QAAO;MAAE;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3H,CAAC,MAAM,IAAI4C,KAAK,CAACjD,IAAI,KAAK,OAAO,EAAE;MACjC,oBACEZ,OAAA;QAAOU,SAAS,EAAC,mBAAmB;QAACkE,QAAQ;QAACjE,KAAK,EAAE2D,UAAW;QAAAzD,QAAA,gBAC9Db,OAAA;UAAQyE,GAAG,EAAEZ,KAAK,CAACjB,GAAI;UAAChC,IAAI,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMd,iBAAiB,GAAGA,CAAC;IAAEG,IAAI;IAAEC,IAAI;IAAEC;EAAQ,CAAC,kBAChDR,OAAA;IAAQU,SAAS,EAAC,8BAA8B;IAACC,KAAK,EAAEF,WAAY;IAACD,OAAO,EAAEA,OAAQ;IAACI,IAAI,EAAC,QAAQ;IAAAC,QAAA,gBAClGb,OAAA,CAACL,IAAI;MAACW,IAAI,EAAEA,IAAK;MAACI,SAAS,EAAC;IAAyB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxDjB,OAAA,CAACL,IAAI;MAACW,IAAI,EAAEA,IAAK;MAACI,SAAS,EAAC;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CjB,OAAA;MAAMU,SAAS,EAAC,oBAAoB;MAAAG,QAAA,EAAEN;IAAI;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CACT;EAED,oBACEjB,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAG,QAAA,eACxBb,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAG,QAAA,gBACxBb,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1Bb,OAAA;UACEyE,GAAG,EAAE,CAAApD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwD,eAAe,KAAIjF,cAAe;UACpDc,SAAS,EAAC,qBAAqB;UAC/BgE,GAAG,EAAE,CAAArD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyD,IAAI,KAAI,SAAU;UACpCnE,KAAK,EAAE;YAAC4D,KAAK,EAAE,MAAM;YAAEQ,MAAM,EAAE;UAAM;QAAE;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACFjB,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1Bb,OAAA;YACEU,SAAS,EAAC,uBAAuB;YACjCsE,IAAI,EAAC,GAAG;YACRC,WAAW,EAAC,sBAAsB;YAClCC,KAAK,EAAE3D,OAAQ;YACf4D,QAAQ,EAAG3C,CAAC,IAAKhB,UAAU,CAACgB,CAAC,CAACE,MAAM,CAACwC,KAAK,CAAE;YAC5CE,SAAS,EAAEvD;UAAe;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACFjB,OAAA;YAAKU,SAAS,EAAC,iCAAiC;YAAAG,QAAA,eAC9Cb,OAAA;cAAOU,SAAS,EAAE,GAAGa,OAAO,CAACmC,MAAM,GAAG7B,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,YAAY,EAAG;cAAAhB,QAAA,GAC1FU,OAAO,CAACmC,MAAM,EAAC,GAAC,EAAC7B,cAAc,EAAC,aACnC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLQ,YAAY,iBACXzB,OAAA;QAAKU,SAAS,EAAC,MAAM;QAAAG,QAAA,eACnBb,OAAA;UAAKU,SAAS,EAAC,mBAAmB;UAAAG,QAAA,GAC/BwD,WAAW,CAAC5C,YAAY,CAAC,eAC1BzB,OAAA;YACEU,SAAS,EAAC,gEAAgE;YAC1EF,OAAO,EAAEA,CAAA,KAAMkB,eAAe,CAAC,IAAI,CAAE;YAAAb,QAAA,eAErCb,OAAA,CAACL,IAAI;cAACW,IAAI,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDjB,OAAA;QACEqF,GAAG,EAAEvD,aAAc;QACnBlB,IAAI,EAAC,MAAM;QACX0E,MAAM,EAAC,SAAS;QAChB5E,SAAS,EAAC,QAAQ;QAClByE,QAAQ,EAAEpC;MAAkB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFjB,OAAA;QACEqF,GAAG,EAAEtD,aAAc;QACnBnB,IAAI,EAAC,MAAM;QACX0E,MAAM,EAAC,SAAS;QAChB5E,SAAS,EAAC,QAAQ;QAClByE,QAAQ,EAAEnC;MAAkB;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAGFjB,OAAA;QAAKU,SAAS,EAAC,mDAAmD;QAAAG,QAAA,gBAChEb,OAAA;UAAKU,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3Bb,OAAA,CAACG,iBAAiB;YAACG,IAAI,EAAC,YAAY;YAACC,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEyC;UAAuB;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFjB,OAAA,CAACG,iBAAiB;YAACG,IAAI,EAAC,WAAW;YAACC,IAAI,EAAC,OAAO;YAACC,OAAO,EAAE6C;UAAuB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACNjB,OAAA;UACEU,SAAS,EAAE,wBAAwB,CAACa,OAAO,CAACiC,IAAI,CAAC,CAAC,IAAI/B,YAAY,KAAK,CAACE,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;UACzHnB,OAAO,EAAE+C,gBAAiB;UAC1BgC,QAAQ,EAAG,CAAChE,OAAO,CAACiC,IAAI,CAAC,CAAC,IAAI,CAAC/B,YAAY,IAAKE,YAAa;UAC7DhB,KAAK,EAAEuB,eAAgB;UAAArB,QAAA,EAEtBc,YAAY,gBACX3B,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA;cAAKU,SAAS,EAAC,uCAAuC;cAAC8E,IAAI,EAAC,QAAQ;cAAA3E,QAAA,eAClEb,OAAA;gBAAMU,SAAS,EAAC,iBAAiB;gBAAAG,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,cAER;UAAA,eAAE,CAAC,gBAEHjB,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA,CAACL,IAAI;cAACW,IAAI,EAAC,UAAU;cAACI,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAE3C;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,CA9MIH,QAAQ;AAAAsE,GAAA,GAARtE,QAAQ;AAgNd,eAAeA,QAAQ;AAAC,IAAAd,EAAA,EAAAa,GAAA,EAAAuE,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}