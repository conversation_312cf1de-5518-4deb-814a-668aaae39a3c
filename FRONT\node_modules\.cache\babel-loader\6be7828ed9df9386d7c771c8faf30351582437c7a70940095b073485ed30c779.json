{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\Feed.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport FeedPost from './FeedPost.jsx';\nimport { getAllFeeds, toggleLike, addComment, getPostComments } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Feed = () => {\n  _s();\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postingNewPost, setPostingNewPost] = useState(false);\n\n  // Comments state\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n\n  // Load initial feeds\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) setLoading(true);else {\n        setLoadingMore(true);\n        setShowLoadingAnimation(true);\n      }\n      const response = await getAllFeeds(page, 5);\n      console.log('Get all feeds response ------------------------', response);\n      if (response.success) {\n        const newPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          comments: [],\n          // Comments will be loaded separately\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n        if (append) {\n          // Add 1 second delay for smooth loading animation\n          setTimeout(() => {\n            setPosts(prev => [...prev, ...newPosts]);\n            setLoadingMore(false);\n            setShowLoadingAnimation(false);\n          }, 1000);\n        } else {\n          setPosts(newPosts);\n          setLoading(false);\n        }\n        setHasMore(response.data.pagination.has_more);\n        setCurrentPage(page);\n      } else {\n        toast.error('Failed to load feeds');\n        setLoadingMore(false);\n        setShowLoadingAnimation(false);\n      }\n    } catch (error) {\n      console.error('Error loading feeds:', error);\n      toast.error('Failed to load feeds');\n      setLoadingMore(false);\n      setShowLoadingAnimation(false);\n    } finally {\n      if (!append) {\n        setLoading(false);\n      }\n    }\n  }, []);\n\n  // Load more posts for infinite scroll\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', {\n        currentPage: currentPage + 1,\n        hasMore\n      });\n      loadFeeds(currentPage + 1, true);\n    }\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        console.log('Scrolled to bottom, checking if should load more...', {\n          loadingMore,\n          hasMore,\n          currentPage\n        });\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadMorePosts, loadingMore, hasMore]);\n\n  // Initial load\n  useEffect(() => {\n    loadFeeds();\n  }, [loadFeeds]);\n\n  // Button styles\n  const buttonStyle = {\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  };\n  const actionButtonStyle = {\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  };\n\n  // Event handlers\n  const handleLike = async postId => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          isLiked: response.data.is_liked,\n          likes: response.data.likes_count\n        } : post));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      } else {\n        setLoadingMoreComments(prev => ({\n          ...prev,\n          [postId]: true\n        }));\n      }\n      const response = await getPostComments(postId, page, 10);\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString()\n        }));\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n        setCommentsPage(prev => ({\n          ...prev,\n          [postId]: page\n        }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n      setLoadingMoreComments(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n    }\n  }, []);\n  const handleComment = postId => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({\n      ...prev,\n      [postId]: !prev[postId]\n    }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n  const loadMoreComments = postId => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n\n  // Infinite scroll for comments\n  const handleCommentsScroll = (postId, e) => {\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = e.target;\n\n    // Check if scrolled to bottom (with 50px threshold)\n    if (scrollTop + clientHeight >= scrollHeight - 50) {\n      const hasMore = commentsHasMore[postId];\n      const isLoading = loadingMoreComments[postId];\n      if (hasMore && !isLoading) {\n        console.log('Scrolled to bottom of comments, loading more...', {\n          postId\n        });\n        loadMoreComments(postId);\n      }\n    }\n  };\n  const handleSubmitComment = async postId => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount + 1\n        } : post));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now'\n        };\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n        setNewComment(prev => ({\n          ...prev,\n          [postId]: ''\n        }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handlePostSubmit = async newPost => {\n    console.log('handlePostSubmit called with:', newPost);\n\n    // Show loading state\n    setPostingNewPost(true);\n\n    // Instead of creating a fake post, let's refresh the feed to get the real data\n    setTimeout(async () => {\n      try {\n        // Refresh the feed to get the latest posts including the new one\n        await loadFeeds(1, false);\n        setPostingNewPost(false);\n      } catch (error) {\n        console.error('Error refreshing feed after post creation:', error);\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n  const handleMyFeedClick = () => {\n    navigate('/user/my-feed');\n  };\n  const handleShare = async post => {\n    try {\n      // Prepare share data\n      const shareData = {\n        title: `${post.user.name}'s Post`,\n        text: post.content || 'Check out this post!',\n        url: post.share_url || window.location.href\n      };\n\n      // Check if Web Share API is supported\n      if (navigator.share) {\n        await navigator.share(shareData);\n        console.log('Shared successfully');\n      } else {\n        // Fallback for browsers that don't support Web Share API\n        // Copy to clipboard\n        const shareText = `${shareData.title}\\n\\n${shareData.text}\\n\\n${shareData.url}`;\n        await navigator.clipboard.writeText(shareText);\n        toast.success('Post link copied to clipboard!');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    const mediaStyle = {\n      width: '100%',\n      maxHeight: '400px'\n    };\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"img\", {\n        src: media.url,\n        className: \"img-fluid rounded\",\n        alt: \"Post media\",\n        style: {\n          ...mediaStyle,\n          objectFit: 'cover'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 14\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"video\", {\n        className: \"img-fluid rounded\",\n        controls: true,\n        style: mediaStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: media.url,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderPostContent = content => {\n    if (!content) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"card-text mb-2\",\n        children: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this);\n  };\n  const renderComments = post => {\n    if (!showComments[post.id]) return null;\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-top pt-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-3\",\n        children: [\"Comments (\", post.commentsCount || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: DefaultProfile,\n          className: \"rounded-circle me-2\",\n          alt: \"Profile\",\n          style: {\n            width: '32px',\n            height: '32px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Write a comment...\",\n            value: newComment[post.id] || '',\n            onChange: e => setNewComment(prev => ({\n              ...prev,\n              [post.id]: e.target.value\n            })),\n            onKeyDown: e => e.key === 'Enter' && handleSubmitComment(post.id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm ms-2 w-auto\",\n          onClick: () => handleSubmitComment(post.id),\n          disabled: !newComment[post.id] || !newComment[post.id].trim(),\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border spinner-border-sm\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted small\",\n          children: \"Loading comments...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          id: `comments-container-${post.id}`,\n          onScroll: e => handleCommentsScroll(post.id, e),\n          children: [comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: comment.avatar,\n              className: \"rounded-circle me-2\",\n              alt: comment.user,\n              style: {\n                width: '32px',\n                height: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-light rounded p-2 flex-grow-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fw-bold\",\n                children: comment.user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: comment.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-muted small mt-1\",\n                children: comment.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this)]\n          }, comment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 17\n          }, this)), isLoadingMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-2 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm text-muted\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more comments...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ms-2 text-muted small\",\n              children: \"Loading more comments...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this);\n  };\n  const ActionButton = ({\n    icon,\n    count,\n    onClick,\n    isLiked,\n    isLast\n  }) => /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,\n    onClick: onClick,\n    style: isLast ? {\n      ...actionButtonStyle,\n      marginRight: 0\n    } : actionButtonStyle,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        icon: icon,\n        style: {\n          fontSize: '1.2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), count && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ms-1\",\n        style: {\n          fontSize: '0.9rem'\n        },\n        children: count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 447,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes fadeIn {\n            from {\n              opacity: 0;\n              transform: translateY(-10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          @keyframes slideInDown {\n            from {\n              opacity: 0;\n              transform: translateY(-30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          .card {\n            transition: all 0.3s ease-in-out;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            onClick: handleMyFeedClick,\n            style: {\n              cursor: 'pointer',\n              padding: '8px',\n              borderRadius: '8px',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseEnter: e => e.currentTarget.style.backgroundColor = '#f8f9fa',\n            onMouseLeave: e => e.currentTarget.style.backgroundColor = 'transparent',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-end me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"My Feed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Share your thoughts and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: DefaultProfile,\n              className: \"rounded-circle\",\n              alt: \"Profile\",\n              style: {\n                width: '50px',\n                height: '50px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeedPost, {\n          onPostSubmit: handlePostSubmit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), postingNewPost && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          style: {\n            animation: 'fadeIn 0.5s ease-in-out',\n            border: '2px dashed #007bff',\n            backgroundColor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-3\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Creating post...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-2\",\n              children: \"Creating your post...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Please wait while we process your content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"Loading posts...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:post-outline\",\n            style: {\n              fontSize: '3rem',\n              color: '#6c757d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"No posts yet. Be the first to share something!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [posts.map((post, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-4\",\n            style: {\n              animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\n              transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: post.user.avatar,\n                  className: \"rounded-circle me-3\",\n                  alt: post.user.name,\n                  style: {\n                    width: '40px',\n                    height: '40px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: post.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: new Date(post.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [renderPostContent(post.content), renderMedia(post.media)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\",\n                  count: post.likes,\n                  onClick: () => handleLike(post.id),\n                  isLiked: post.isLiked\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:comment-outline\",\n                  count: post.commentsCount || 0,\n                  onClick: () => handleComment(post.id)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:share-variant-outline\",\n                  onClick: () => handleShare(post),\n                  isLast: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 21\n              }, this), renderComments(post)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 17\n          }, this)), showLoadingAnimation && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            style: {\n              animation: 'fadeIn 0.5s ease-in-out'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more posts...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-primary mb-0\",\n              children: \"Loading more posts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 17\n          }, this), !showLoadingAnimation && !loadingMore && hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline-primary\",\n              onClick: loadMorePosts,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"mdi:chevron-down\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 21\n              }, this), \"Load More Posts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 17\n          }, this), !hasMore && posts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"You've reached the end of the feed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 460,\n    columnNumber: 5\n  }, this);\n};\n_s(Feed, \"hZ9Az+t2VDJ/r1YzcRLvtWJKv4g=\", false, function () {\n  return [useNavigate];\n});\n_c = Feed;\nexport default Feed;\nvar _c;\n$RefreshReg$(_c, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Icon", "useNavigate", "DefaultProfile", "FeedPost", "getAllFeeds", "toggleLike", "addComment", "getPostComments", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Feed", "_s", "navigate", "posts", "setPosts", "loading", "setLoading", "loadingMore", "setLoadingMore", "currentPage", "setCurrentPage", "hasMore", "setHasMore", "showLoadingAnimation", "setShowLoadingAnimation", "newComment", "setNewComment", "showComments", "setShowComments", "postingNewPost", "setPostingNewPost", "postComments", "setPostComments", "commentsLoading", "setCommentsLoading", "commentsPage", "setCommentsPage", "commentsHasMore", "setCommentsHasMore", "loadingMoreComments", "setLoadingMoreComments", "loadFeeds", "page", "append", "response", "console", "log", "success", "newPosts", "data", "map", "post", "id", "user", "name", "user_name", "avatar", "user_avatar", "content", "description", "media", "media_url", "type", "media_type", "url", "isLiked", "is_liked_by_user", "likes", "likes_count", "comments", "commentsCount", "comments_count", "created_at", "setTimeout", "prev", "pagination", "has_more", "error", "loadMorePosts", "handleScroll", "scrollTop", "document", "documentElement", "scrollHeight", "clientHeight", "window", "addEventListener", "removeEventListener", "buttonStyle", "backgroundColor", "borderColor", "actionButtonStyle", "flex", "marginRight", "handleLike", "postId", "is_liked", "loadPostComments", "newComments", "comment", "text", "timestamp", "Date", "commented_at", "toLocaleDateString", "handleComment", "isOpening", "loadMoreComments", "handleCommentsScroll", "e", "target", "isLoading", "handleSubmitComment", "commentText", "trim", "newCommentObj", "handlePostSubmit", "newPost", "handleMyFeedClick", "handleShare", "shareData", "title", "share_url", "location", "href", "navigator", "share", "shareText", "clipboard", "writeText", "renderMedia", "mediaStyle", "width", "maxHeight", "src", "className", "alt", "style", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "controls", "children", "renderPostContent", "renderComments", "isLoadingMore", "height", "placeholder", "value", "onChange", "onKeyDown", "key", "onClick", "disabled", "icon", "role", "overflowY", "onScroll", "ActionButton", "count", "isLast", "fontSize", "cursor", "padding", "borderRadius", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "onPostSubmit", "animation", "border", "length", "color", "index", "transform", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/Feed.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react'\nimport { Icon } from '@iconify/react'\nimport { useNavigate } from 'react-router-dom'\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\nimport FeedPost from './FeedPost.jsx'\nimport { getAllFeeds, toggleLike, addComment, getPostComments } from '../../../services/feedServices'\nimport { toast } from 'react-toastify'\n\nconst Feed = () => {\n  const navigate = useNavigate();\n\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postingNewPost, setPostingNewPost] = useState(false);\n\n  // Comments state\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [commentsPage, setCommentsPage] = useState({}); // Current page for each post\n  const [commentsHasMore, setCommentsHasMore] = useState({}); // Whether more comments exist\n  const [loadingMoreComments, setLoadingMoreComments] = useState({}); // Loading more comments state\n\n  // Load initial feeds\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) setLoading(true);\n      else {\n        setLoadingMore(true);\n        setShowLoadingAnimation(true);\n      }\n\n      const response = await getAllFeeds(page, 5);\n      console.log('Get all feeds response ------------------------', response);\n\n      if (response.success) {\n        const newPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          comments: [], // Comments will be loaded separately\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n\n        if (append) {\n          // Add 1 second delay for smooth loading animation\n          setTimeout(() => {\n            setPosts(prev => [...prev, ...newPosts]);\n            setLoadingMore(false);\n            setShowLoadingAnimation(false);\n          }, 1000);\n        } else {\n          setPosts(newPosts);\n          setLoading(false);\n        }\n\n        setHasMore(response.data.pagination.has_more);\n        setCurrentPage(page);\n      } else {\n        toast.error('Failed to load feeds');\n        setLoadingMore(false);\n        setShowLoadingAnimation(false);\n      }\n    } catch (error) {\n      console.error('Error loading feeds:', error);\n      toast.error('Failed to load feeds');\n      setLoadingMore(false);\n      setShowLoadingAnimation(false);\n    } finally {\n      if (!append) {\n        setLoading(false);\n      }\n    }\n  }, []);\n\n  // Load more posts for infinite scroll\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });\n      loadFeeds(currentPage + 1, true);\n    }\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n      \n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        console.log('Scrolled to bottom, checking if should load more...', {\n          loadingMore,\n          hasMore,\n          currentPage\n        });\n        \n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadMorePosts, loadingMore, hasMore]);\n\n  // Initial load\n  useEffect(() => {\n    loadFeeds();\n  }, [loadFeeds]);\n\n  // Button styles\n  const buttonStyle = {\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  };\n\n  const actionButtonStyle = {\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  };\n\n  // Event handlers\n  const handleLike = async (postId) => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? {\n                ...post,\n                isLiked: response.data.is_liked,\n                likes: response.data.likes_count\n              }\n            : post\n        ));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async (postId, page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setCommentsLoading(prev => ({ ...prev, [postId]: true }));\n      } else {\n        setLoadingMoreComments(prev => ({ ...prev, [postId]: true }));\n      }\n\n      const response = await getPostComments(postId, page, 10);\n\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString()\n        }));\n\n        if (append) {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: [...(prev[postId] || []), ...newComments]\n          }));\n        } else {\n          setPostComments(prev => ({\n            ...prev,\n            [postId]: newComments\n          }));\n        }\n\n        setCommentsPage(prev => ({ ...prev, [postId]: page }));\n        setCommentsHasMore(prev => ({\n          ...prev,\n          [postId]: response.data.pagination.has_more\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({ ...prev, [postId]: false }));\n      setLoadingMoreComments(prev => ({ ...prev, [postId]: false }));\n    }\n  }, []);\n\n  const handleComment = (postId) => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId, 1);\n    }\n  };\n\n  const loadMoreComments = (postId) => {\n    const currentPage = commentsPage[postId] || 1;\n    loadPostComments(postId, currentPage + 1, true);\n  };\n\n  // Infinite scroll for comments\n  const handleCommentsScroll = (postId, e) => {\n    const { scrollTop, scrollHeight, clientHeight } = e.target;\n    \n    // Check if scrolled to bottom (with 50px threshold)\n    if (scrollTop + clientHeight >= scrollHeight - 50) {\n      const hasMore = commentsHasMore[postId];\n      const isLoading = loadingMoreComments[postId];\n      \n      if (hasMore && !isLoading) {\n        console.log('Scrolled to bottom of comments, loading more...', { postId });\n        loadMoreComments(postId);\n      }\n    }\n  };\n\n  const handleSubmitComment = async (postId) => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post =>\n          post.id === postId\n            ? { ...post, commentsCount: post.commentsCount + 1 }\n            : post\n        ));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now'\n        };\n\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n\n  const handlePostSubmit = async (newPost) => {\n    console.log('handlePostSubmit called with:', newPost);\n    \n    // Show loading state\n    setPostingNewPost(true);\n    \n    // Instead of creating a fake post, let's refresh the feed to get the real data\n    setTimeout(async () => {\n      try {\n        // Refresh the feed to get the latest posts including the new one\n        await loadFeeds(1, false);\n        setPostingNewPost(false);\n      } catch (error) {\n        console.error('Error refreshing feed after post creation:', error);\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n\n\n\n  const handleMyFeedClick = () => {\n    navigate('/user/my-feed');\n  };\n\n  const handleShare = async (post) => {\n    try {\n      // Prepare share data\n      const shareData = {\n        title: `${post.user.name}'s Post`,\n        text: post.content || 'Check out this post!',\n        url: post.share_url || window.location.href\n      };\n\n      // Check if Web Share API is supported\n      if (navigator.share) {\n        await navigator.share(shareData);\n        console.log('Shared successfully');\n      } else {\n        // Fallback for browsers that don't support Web Share API\n        // Copy to clipboard\n        const shareText = `${shareData.title}\\n\\n${shareData.text}\\n\\n${shareData.url}`;\n        await navigator.clipboard.writeText(shareText);\n        toast.success('Post link copied to clipboard!');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = (media) => {\n    if (!media) return null;\n\n    const mediaStyle = { width: '100%', maxHeight: '400px' };\n\n    if (media.type === 'image') {\n      return <img src={media.url} className=\"img-fluid rounded\" alt=\"Post media\" style={{...mediaStyle, objectFit: 'cover'}} />;\n    } else if (media.type === 'video') {\n      return (\n        <video className=\"img-fluid rounded\" controls style={mediaStyle}>\n          <source src={media.url} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n      );\n    }\n    return null;\n  };\n\n  const renderPostContent = (content) => {\n    if (!content) return null;\n\n    return (\n      <div>\n        <p className=\"card-text mb-2\">{content}</p>\n      </div>\n    );\n  };\n\n  const renderComments = (post) => {\n    if (!showComments[post.id]) return null;\n\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    const isLoadingMore = loadingMoreComments[post.id];\n    const hasMore = commentsHasMore[post.id];\n\n    return (\n      <div className=\"border-top pt-3 mt-3\">\n        <h6 className=\"mb-3\">Comments ({post.commentsCount || 0})</h6>\n\n        {/* Comment Input */}\n        <div className=\"d-flex mb-3\">\n          <img src={DefaultProfile} className=\"rounded-circle me-2\" alt=\"Profile\" style={{width: '32px', height: '32px'}} />\n          <div className=\"flex-grow-1\">\n            <input\n              type=\"text\"\n              className=\"form-control\"\n              placeholder=\"Write a comment...\"\n              value={newComment[post.id] || ''}\n              onChange={(e) => setNewComment(prev => ({ ...prev, [post.id]: e.target.value }))}\n              onKeyDown={(e) => e.key === 'Enter' && handleSubmitComment(post.id)}\n            />\n          </div>\n          <button\n            className=\"btn btn-primary btn-sm ms-2 w-auto\"\n            onClick={() => handleSubmitComment(post.id)}\n            disabled={!newComment[post.id] || !newComment[post.id].trim()}\n          >\n            <Icon icon=\"mdi:send\" />\n          </button>\n        </div>\n\n        {/* Comments Loading State */}\n        {isLoading ? (\n          <div className=\"text-center py-3\">\n            <div className=\"spinner-border spinner-border-sm\" role=\"status\">\n              <span className=\"visually-hidden\">Loading comments...</span>\n            </div>\n            <p className=\"mt-2 text-muted small\">Loading comments...</p>\n          </div>\n        ) : (\n          <>\n            {/* Comments Container with Scroll */}\n            <div \n              style={{ maxHeight: '300px', overflowY: 'auto' }} \n              id={`comments-container-${post.id}`}\n              onScroll={(e) => handleCommentsScroll(post.id, e)}\n            >\n              {/* Existing Comments */}\n              {comments.map(comment => (\n                <div key={comment.id} className=\"d-flex mb-2\">\n                  <img src={comment.avatar} className=\"rounded-circle me-2\" alt={comment.user} style={{width: '32px', height: '32px'}} />\n                  <div className=\"bg-light rounded p-2 flex-grow-1\">\n                    <div className=\"fw-bold\">{comment.user}</div>\n                    <div>{comment.text}</div>\n                    <div className=\"text-muted small mt-1\">{comment.timestamp}</div>\n                  </div>\n                </div>\n              ))}\n\n              {/* Loading More Comments Indicator */}\n              {isLoadingMore && (\n                <div className=\"text-center mt-2 py-2\">\n                  <div className=\"spinner-border spinner-border-sm text-muted\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading more comments...</span>\n                  </div>\n                  <span className=\"ms-2 text-muted small\">Loading more comments...</span>\n                </div>\n              )}\n            </div>\n          </>\n        )}\n      </div>\n    );\n  };\n\n  const ActionButton = ({ icon, count, onClick, isLiked, isLast }) => (\n    <button \n      className={`btn border ${isLiked ? 'text-danger' : 'text-muted'}`}\n      onClick={onClick}\n      style={isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle}\n    >\n      <div className=\"d-flex align-items-center justify-content-center\">\n        <Icon icon={icon} style={{fontSize: '1.2rem'}} />\n        {count && <span className=\"ms-1\" style={{fontSize: '0.9rem'}}>{count}</span>}\n      </div>\n    </button>\n  );\n\n  return (\n    <div className=\"container py-4\">\n      <style>\n        {`\n          @keyframes fadeIn {\n            from {\n              opacity: 0;\n              transform: translateY(-10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          @keyframes slideInDown {\n            from {\n              opacity: 0;\n              transform: translateY(-30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          .card {\n            transition: all 0.3s ease-in-out;\n          }\n        `}\n      </style>\n      <div className=\"row justify-content-center\">\n        <div className=\"col-md-8\">\n          {/* Profile Header */}\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <div></div>\n            <div \n              className=\"d-flex align-items-center\"\n              onClick={handleMyFeedClick}\n              style={{ \n                cursor: 'pointer',\n                padding: '8px',\n                borderRadius: '8px',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}\n              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}\n            >\n              <div className=\"text-end me-3\">\n                <h5 className=\"mb-0\">My Feed</h5>\n                <small className=\"text-muted\">Share your thoughts and updates</small>\n              </div>\n              <img src={DefaultProfile} className=\"rounded-circle\" alt=\"Profile\" style={{width: '50px', height: '50px'}} />\n            </div>\n          </div>\n\n          {/* Create Post Component */}\n          <FeedPost onPostSubmit={handlePostSubmit} />\n\n          {/* New Post Loading State */}\n          {postingNewPost && (\n            <div className=\"card mb-4\" style={{\n              animation: 'fadeIn 0.5s ease-in-out',\n              border: '2px dashed #007bff',\n              backgroundColor: '#f8f9fa'\n            }}>\n              <div className=\"card-body text-center py-4\">\n                <div className=\"spinner-border text-primary mb-3\" role=\"status\">\n                  <span className=\"visually-hidden\">Creating post...</span>\n                </div>\n                <h6 className=\"text-primary mb-2\">Creating your post...</h6>\n                <p className=\"text-muted mb-0\">Please wait while we process your content</p>\n              </div>\n            </div>\n          )}\n\n          {/* Loading State */}\n          {loading ? (\n            <div className=\"text-center py-4\">\n              <div className=\"spinner-border\" role=\"status\">\n                <span className=\"visually-hidden\">Loading...</span>\n              </div>\n              <p className=\"mt-2 text-muted\">Loading posts...</p>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <Icon icon=\"mdi:post-outline\" style={{ fontSize: '3rem', color: '#6c757d' }} />\n              <p className=\"mt-2 text-muted\">No posts yet. Be the first to share something!</p>\n            </div>\n          ) : (\n            <>\n              {/* Posts Feed */}\n              {posts.map((post, index) => (\n                <div \n                  key={post.id} \n                  className=\"card mb-4\"\n                  style={{\n                    animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\n                    transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\n                  }}\n                >\n                  <div className=\"card-body\">\n                    {/* Post Header */}\n                    <div className=\"d-flex align-items-center mb-3\">\n                      <img src={post.user.avatar} className=\"rounded-circle me-3\" alt={post.user.name} style={{width: '40px', height: '40px'}} />\n                      <div className=\"flex-grow-1\">\n                        <h6 className=\"mb-0\">{post.user.name}</h6>\n                        <small className=\"text-muted\">{new Date(post.created_at).toLocaleDateString()}</small>\n                      </div>\n                    </div>\n\n                    {/* Post Content */}\n                    <div className=\"mb-3\">\n                      {renderPostContent(post.content)}\n                      {renderMedia(post.media)}\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className=\"d-flex justify-content-between\">\n                      <ActionButton\n                        icon={post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\"}\n                        count={post.likes}\n                        onClick={() => handleLike(post.id)}\n                        isLiked={post.isLiked}\n                      />\n                      <ActionButton\n                        icon=\"mdi:comment-outline\"\n                        count={post.commentsCount || 0}\n                        onClick={() => handleComment(post.id)}\n                      />\n                      <ActionButton\n                        icon=\"mdi:share-variant-outline\"\n                        onClick={() => handleShare(post)}\n                        isLast={true}\n                      />\n                    </div>\n\n                    {/* Comments Section */}\n                    {renderComments(post)}\n                  </div>\n                </div>\n              ))}\n\n              {/* Load More Button */}\n              {showLoadingAnimation && (\n                <div className=\"text-center py-4\" style={{\n                  animation: 'fadeIn 0.5s ease-in-out'\n                }}>\n                  <div className=\"spinner-border text-primary mb-2\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading more posts...</span>\n                  </div>\n                  <p className=\"text-primary mb-0\">Loading more posts...</p>\n                </div>\n              )}\n\n              {!showLoadingAnimation && !loadingMore && hasMore && (\n                <div className=\"text-center py-3\">\n                  <button \n                    className=\"btn btn-outline-primary\"\n                    onClick={loadMorePosts}\n                  >\n                    <Icon icon=\"mdi:chevron-down\" className=\"me-2\" />\n                    Load More Posts\n                  </button>\n                </div>\n              )}\n\n              {!hasMore && posts.length > 0 && (\n                <div className=\"text-center py-3\">\n                  <p className=\"text-muted\">You've reached the end of the feed!</p>\n                </div>\n              )}\n\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Feed;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,QAAQ,gCAAgC;AACrG,SAASC,KAAK,QAAQ,gBAAgB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpE;EACA,MAAM+C,SAAS,GAAG7C,WAAW,CAAC,OAAO8C,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAChE,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE1B,UAAU,CAAC,IAAI,CAAC,CAAC,KAC5B;QACHE,cAAc,CAAC,IAAI,CAAC;QACpBM,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEA,MAAMoB,QAAQ,GAAG,MAAM3C,WAAW,CAACyC,IAAI,EAAE,CAAC,CAAC;MAC3CG,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEF,QAAQ,CAAC;MAExE,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMC,QAAQ,GAAGJ,QAAQ,CAACK,IAAI,CAACpC,KAAK,CAACqC,GAAG,CAACC,IAAI,KAAK;UAChDC,EAAE,EAAED,IAAI,CAACC,EAAE;UACXC,IAAI,EAAE;YACJC,IAAI,EAAEH,IAAI,CAACI,SAAS;YACpBC,MAAM,EAAEL,IAAI,CAACM,WAAW,IAAI1D;UAC9B,CAAC;UACD2D,OAAO,EAAEP,IAAI,CAACQ,WAAW;UACzBC,KAAK,EAAET,IAAI,CAACU,SAAS,GAAG;YACtBC,IAAI,EAAEX,IAAI,CAACY,UAAU;YACrBC,GAAG,EAAEb,IAAI,CAACU;UACZ,CAAC,GAAG,IAAI;UACRI,OAAO,EAAEd,IAAI,CAACe,gBAAgB,KAAK,CAAC;UACpCC,KAAK,EAAEhB,IAAI,CAACiB,WAAW;UACvBC,QAAQ,EAAE,EAAE;UAAE;UACdC,aAAa,EAAEnB,IAAI,CAACoB,cAAc;UAClCC,UAAU,EAAErB,IAAI,CAACqB;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI7B,MAAM,EAAE;UACV;UACA8B,UAAU,CAAC,MAAM;YACf3D,QAAQ,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG1B,QAAQ,CAAC,CAAC;YACxC9B,cAAc,CAAC,KAAK,CAAC;YACrBM,uBAAuB,CAAC,KAAK,CAAC;UAChC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLV,QAAQ,CAACkC,QAAQ,CAAC;UAClBhC,UAAU,CAAC,KAAK,CAAC;QACnB;QAEAM,UAAU,CAACsB,QAAQ,CAACK,IAAI,CAAC0B,UAAU,CAACC,QAAQ,CAAC;QAC7CxD,cAAc,CAACsB,IAAI,CAAC;MACtB,CAAC,MAAM;QACLrC,KAAK,CAACwE,KAAK,CAAC,sBAAsB,CAAC;QACnC3D,cAAc,CAAC,KAAK,CAAC;QACrBM,uBAAuB,CAAC,KAAK,CAAC;MAChC;IACF,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CxE,KAAK,CAACwE,KAAK,CAAC,sBAAsB,CAAC;MACnC3D,cAAc,CAAC,KAAK,CAAC;MACrBM,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,SAAS;MACR,IAAI,CAACmB,MAAM,EAAE;QACX3B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8D,aAAa,GAAGlF,WAAW,CAAC,MAAM;IACtC,IAAI,CAACqB,WAAW,IAAII,OAAO,EAAE;MAC3BwB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAE3B,WAAW,EAAEA,WAAW,GAAG,CAAC;QAAEE;MAAQ,CAAC,CAAC;MAC/EoB,SAAS,CAACtB,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;IAClC;EACF,CAAC,EAAE,CAACsB,SAAS,EAAExB,WAAW,EAAEI,OAAO,EAAEF,WAAW,CAAC,CAAC;;EAElD;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMoF,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAACF,SAAS;MACpD,MAAMG,YAAY,GAAGF,QAAQ,CAACC,eAAe,CAACC,YAAY;MAC1D,MAAMC,YAAY,GAAGH,QAAQ,CAACC,eAAe,CAACE,YAAY;;MAE1D;MACA,IAAIJ,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,GAAG,EAAE;QAClDtC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;UACjE7B,WAAW;UACXI,OAAO;UACPF;QACF,CAAC,CAAC;QAEF,IAAI,CAACF,WAAW,IAAII,OAAO,EAAE;UAC3ByD,aAAa,CAAC,CAAC;QACjB;MACF;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAC/C,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;EACjE,CAAC,EAAE,CAACD,aAAa,EAAE7D,WAAW,EAAEI,OAAO,CAAC,CAAC;;EAEzC;EACA1B,SAAS,CAAC,MAAM;IACd8C,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM+C,WAAW,GAAG;IAClBC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,iBAAiB,GAAG;IACxBC,IAAI,EAAE,CAAC;IACPC,WAAW,EAAE,MAAM;IACnB,GAAGL;EACL,CAAC;;EAED;EACA,MAAMM,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAI;MACF,MAAMnD,QAAQ,GAAG,MAAM1C,UAAU,CAAC6F,MAAM,CAAC;MACzC,IAAInD,QAAQ,CAACG,OAAO,EAAE;QACpBjC,QAAQ,CAACD,KAAK,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACC,EAAE,KAAK2C,MAAM,GACd;UACE,GAAG5C,IAAI;UACPc,OAAO,EAAErB,QAAQ,CAACK,IAAI,CAAC+C,QAAQ;UAC/B7B,KAAK,EAAEvB,QAAQ,CAACK,IAAI,CAACmB;QACvB,CAAC,GACDjB,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACL9C,KAAK,CAACwE,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CxE,KAAK,CAACwE,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAGrG,WAAW,CAAC,OAAOmG,MAAM,EAAErD,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAC/E,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE;QACdR,kBAAkB,CAACwC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC3D,CAAC,MAAM;QACLvD,sBAAsB,CAACkC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAK,CAAC,CAAC,CAAC;MAC/D;MAEA,MAAMnD,QAAQ,GAAG,MAAMxC,eAAe,CAAC2F,MAAM,EAAErD,IAAI,EAAE,EAAE,CAAC;MAExD,IAAIE,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMmD,WAAW,GAAGtD,QAAQ,CAACK,IAAI,CAACoB,QAAQ,CAACnB,GAAG,CAACiD,OAAO,KAAK;UACzD/C,EAAE,EAAE+C,OAAO,CAAC/C,EAAE;UACdC,IAAI,EAAE8C,OAAO,CAAC5C,SAAS;UACvBC,MAAM,EAAE2C,OAAO,CAAC1C,WAAW,IAAI1D,cAAc;UAC7CqG,IAAI,EAAED,OAAO,CAACA,OAAO;UACrBE,SAAS,EAAE,IAAIC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAI7D,MAAM,EAAE;UACVX,eAAe,CAAC0C,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACqB,MAAM,GAAG,CAAC,IAAIrB,IAAI,CAACqB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAGG,WAAW;UACpD,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLlE,eAAe,CAAC0C,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACqB,MAAM,GAAGG;UACZ,CAAC,CAAC,CAAC;QACL;QAEA9D,eAAe,CAACsC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAGrD;QAAK,CAAC,CAAC,CAAC;QACtDJ,kBAAkB,CAACoC,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAGnD,QAAQ,CAACK,IAAI,CAAC0B,UAAU,CAACC;QACrC,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLvE,KAAK,CAACwE,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CxE,KAAK,CAACwE,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACR3C,kBAAkB,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;MAC1DvD,sBAAsB,CAACkC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,aAAa,GAAIV,MAAM,IAAK;IAChC,MAAMW,SAAS,GAAG,CAAC/E,YAAY,CAACoE,MAAM,CAAC;IACvCnE,eAAe,CAAC8C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACqB,MAAM,GAAG,CAACrB,IAAI,CAACqB,MAAM;IAAE,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIW,SAAS,IAAI,CAAC3E,YAAY,CAACgE,MAAM,CAAC,EAAE;MACtCE,gBAAgB,CAACF,MAAM,EAAE,CAAC,CAAC;IAC7B;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAIZ,MAAM,IAAK;IACnC,MAAM5E,WAAW,GAAGgB,YAAY,CAAC4D,MAAM,CAAC,IAAI,CAAC;IAC7CE,gBAAgB,CAACF,MAAM,EAAE5E,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;EACjD,CAAC;;EAED;EACA,MAAMyF,oBAAoB,GAAGA,CAACb,MAAM,EAAEc,CAAC,KAAK;IAC1C,MAAM;MAAE7B,SAAS;MAAEG,YAAY;MAAEC;IAAa,CAAC,GAAGyB,CAAC,CAACC,MAAM;;IAE1D;IACA,IAAI9B,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,EAAE,EAAE;MACjD,MAAM9D,OAAO,GAAGgB,eAAe,CAAC0D,MAAM,CAAC;MACvC,MAAMgB,SAAS,GAAGxE,mBAAmB,CAACwD,MAAM,CAAC;MAE7C,IAAI1E,OAAO,IAAI,CAAC0F,SAAS,EAAE;QACzBlE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;UAAEiD;QAAO,CAAC,CAAC;QAC1EY,gBAAgB,CAACZ,MAAM,CAAC;MAC1B;IACF;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAOjB,MAAM,IAAK;IAC5C,MAAMkB,WAAW,GAAGxF,UAAU,CAACsE,MAAM,CAAC;IACtC,IAAI,CAACkB,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;MACvC7G,KAAK,CAACwE,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMzC,UAAU,CAAC4F,MAAM,EAAEkB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7D,IAAItE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACAjC,QAAQ,CAACD,KAAK,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACC,EAAE,KAAK2C,MAAM,GACd;UAAE,GAAG5C,IAAI;UAAEmB,aAAa,EAAEnB,IAAI,CAACmB,aAAa,GAAG;QAAE,CAAC,GAClDnB,IACN,CAAC,CAAC;;QAEF;QACA,MAAMgE,aAAa,GAAG;UACpB/D,EAAE,EAAER,QAAQ,CAACK,IAAI,CAACkD,OAAO,CAAC/C,EAAE;UAC5BC,IAAI,EAAET,QAAQ,CAACK,IAAI,CAACkD,OAAO,CAAC5C,SAAS;UACrCC,MAAM,EAAEZ,QAAQ,CAACK,IAAI,CAACkD,OAAO,CAAC1C,WAAW,IAAI1D,cAAc;UAC3DqG,IAAI,EAAExD,QAAQ,CAACK,IAAI,CAACkD,OAAO,CAACA,OAAO;UACnCE,SAAS,EAAE;QACb,CAAC;QAEDrE,eAAe,CAAC0C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAG,CAACoB,aAAa,EAAE,IAAIzC,IAAI,CAACqB,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEHrE,aAAa,CAACgD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAG,CAAC,CAAC,CAAC;QAClD1F,KAAK,CAAC0C,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,MAAM;QACL1C,KAAK,CAACwE,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxE,KAAK,CAACwE,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1CxE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuE,OAAO,CAAC;;IAErD;IACAvF,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA2C,UAAU,CAAC,YAAY;MACrB,IAAI;QACF;QACA,MAAMhC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;QACzBX,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAC,CAAC,OAAO+C,KAAK,EAAE;QACdhC,OAAO,CAACgC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE/C,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;EAID,MAAMwF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1G,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM2G,WAAW,GAAG,MAAOpE,IAAI,IAAK;IAClC,IAAI;MACF;MACA,MAAMqE,SAAS,GAAG;QAChBC,KAAK,EAAE,GAAGtE,IAAI,CAACE,IAAI,CAACC,IAAI,SAAS;QACjC8C,IAAI,EAAEjD,IAAI,CAACO,OAAO,IAAI,sBAAsB;QAC5CM,GAAG,EAAEb,IAAI,CAACuE,SAAS,IAAIrC,MAAM,CAACsC,QAAQ,CAACC;MACzC,CAAC;;MAED;MACA,IAAIC,SAAS,CAACC,KAAK,EAAE;QACnB,MAAMD,SAAS,CAACC,KAAK,CAACN,SAAS,CAAC;QAChC3E,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC,CAAC,MAAM;QACL;QACA;QACA,MAAMiF,SAAS,GAAG,GAAGP,SAAS,CAACC,KAAK,OAAOD,SAAS,CAACpB,IAAI,OAAOoB,SAAS,CAACxD,GAAG,EAAE;QAC/E,MAAM6D,SAAS,CAACG,SAAS,CAACC,SAAS,CAACF,SAAS,CAAC;QAC9C1H,KAAK,CAAC0C,OAAO,CAAC,gCAAgC,CAAC;MACjD;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAIA,KAAK,CAACvB,IAAI,KAAK,YAAY,EAAE;QAC/BjD,KAAK,CAACwE,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;EACF,CAAC;;EAED;EACA,MAAMqD,WAAW,GAAItE,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMuE,UAAU,GAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ,CAAC;IAExD,IAAIzE,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B,oBAAOvD,OAAA;QAAK+H,GAAG,EAAE1E,KAAK,CAACI,GAAI;QAACuE,SAAS,EAAC,mBAAmB;QAACC,GAAG,EAAC,YAAY;QAACC,KAAK,EAAE;UAAC,GAAGN,UAAU;UAAEO,SAAS,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3H,CAAC,MAAM,IAAIlF,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MACjC,oBACEvD,OAAA;QAAOgI,SAAS,EAAC,mBAAmB;QAACQ,QAAQ;QAACN,KAAK,EAAEN,UAAW;QAAAa,QAAA,gBAC9DzI,OAAA;UAAQ+H,GAAG,EAAE1E,KAAK,CAACI,GAAI;UAACF,IAAI,EAAC;QAAW;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,iBAAiB,GAAIvF,OAAO,IAAK;IACrC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,oBACEnD,OAAA;MAAAyI,QAAA,eACEzI,OAAA;QAAGgI,SAAS,EAAC,gBAAgB;QAAAS,QAAA,EAAEtF;MAAO;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEV,CAAC;EAED,MAAMI,cAAc,GAAI/F,IAAI,IAAK;IAC/B,IAAI,CAACxB,YAAY,CAACwB,IAAI,CAACC,EAAE,CAAC,EAAE,OAAO,IAAI;IAEvC,MAAMiB,QAAQ,GAAGtC,YAAY,CAACoB,IAAI,CAACC,EAAE,CAAC,IAAI,EAAE;IAC5C,MAAM2D,SAAS,GAAG9E,eAAe,CAACkB,IAAI,CAACC,EAAE,CAAC;IAC1C,MAAM+F,aAAa,GAAG5G,mBAAmB,CAACY,IAAI,CAACC,EAAE,CAAC;IAClD,MAAM/B,OAAO,GAAGgB,eAAe,CAACc,IAAI,CAACC,EAAE,CAAC;IAExC,oBACE7C,OAAA;MAAKgI,SAAS,EAAC,sBAAsB;MAAAS,QAAA,gBACnCzI,OAAA;QAAIgI,SAAS,EAAC,MAAM;QAAAS,QAAA,GAAC,YAAU,EAAC7F,IAAI,CAACmB,aAAa,IAAI,CAAC,EAAC,GAAC;MAAA;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG9DvI,OAAA;QAAKgI,SAAS,EAAC,aAAa;QAAAS,QAAA,gBAC1BzI,OAAA;UAAK+H,GAAG,EAAEvI,cAAe;UAACwI,SAAS,EAAC,qBAAqB;UAACC,GAAG,EAAC,SAAS;UAACC,KAAK,EAAE;YAACL,KAAK,EAAE,MAAM;YAAEgB,MAAM,EAAE;UAAM;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHvI,OAAA;UAAKgI,SAAS,EAAC,aAAa;UAAAS,QAAA,eAC1BzI,OAAA;YACEuD,IAAI,EAAC,MAAM;YACXyE,SAAS,EAAC,cAAc;YACxBc,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE7H,UAAU,CAAC0B,IAAI,CAACC,EAAE,CAAC,IAAI,EAAG;YACjCmG,QAAQ,EAAG1C,CAAC,IAAKnF,aAAa,CAACgD,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE,CAACvB,IAAI,CAACC,EAAE,GAAGyD,CAAC,CAACC,MAAM,CAACwC;YAAM,CAAC,CAAC,CAAE;YACjFE,SAAS,EAAG3C,CAAC,IAAKA,CAAC,CAAC4C,GAAG,KAAK,OAAO,IAAIzC,mBAAmB,CAAC7D,IAAI,CAACC,EAAE;UAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvI,OAAA;UACEgI,SAAS,EAAC,oCAAoC;UAC9CmB,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,CAAC7D,IAAI,CAACC,EAAE,CAAE;UAC5CuG,QAAQ,EAAE,CAAClI,UAAU,CAAC0B,IAAI,CAACC,EAAE,CAAC,IAAI,CAAC3B,UAAU,CAAC0B,IAAI,CAACC,EAAE,CAAC,CAAC8D,IAAI,CAAC,CAAE;UAAA8B,QAAA,eAE9DzI,OAAA,CAACV,IAAI;YAAC+J,IAAI,EAAC;UAAU;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/B,SAAS,gBACRxG,OAAA;QAAKgI,SAAS,EAAC,kBAAkB;QAAAS,QAAA,gBAC/BzI,OAAA;UAAKgI,SAAS,EAAC,kCAAkC;UAACsB,IAAI,EAAC,QAAQ;UAAAb,QAAA,eAC7DzI,OAAA;YAAMgI,SAAS,EAAC,iBAAiB;YAAAS,QAAA,EAAC;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNvI,OAAA;UAAGgI,SAAS,EAAC,uBAAuB;UAAAS,QAAA,EAAC;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,gBAENvI,OAAA,CAAAE,SAAA;QAAAuI,QAAA,eAEEzI,OAAA;UACEkI,KAAK,EAAE;YAAEJ,SAAS,EAAE,OAAO;YAAEyB,SAAS,EAAE;UAAO,CAAE;UACjD1G,EAAE,EAAE,sBAAsBD,IAAI,CAACC,EAAE,EAAG;UACpC2G,QAAQ,EAAGlD,CAAC,IAAKD,oBAAoB,CAACzD,IAAI,CAACC,EAAE,EAAEyD,CAAC,CAAE;UAAAmC,QAAA,GAGjD3E,QAAQ,CAACnB,GAAG,CAACiD,OAAO,iBACnB5F,OAAA;YAAsBgI,SAAS,EAAC,aAAa;YAAAS,QAAA,gBAC3CzI,OAAA;cAAK+H,GAAG,EAAEnC,OAAO,CAAC3C,MAAO;cAAC+E,SAAS,EAAC,qBAAqB;cAACC,GAAG,EAAErC,OAAO,CAAC9C,IAAK;cAACoF,KAAK,EAAE;gBAACL,KAAK,EAAE,MAAM;gBAAEgB,MAAM,EAAE;cAAM;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvHvI,OAAA;cAAKgI,SAAS,EAAC,kCAAkC;cAAAS,QAAA,gBAC/CzI,OAAA;gBAAKgI,SAAS,EAAC,SAAS;gBAAAS,QAAA,EAAE7C,OAAO,CAAC9C;cAAI;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CvI,OAAA;gBAAAyI,QAAA,EAAM7C,OAAO,CAACC;cAAI;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBvI,OAAA;gBAAKgI,SAAS,EAAC,uBAAuB;gBAAAS,QAAA,EAAE7C,OAAO,CAACE;cAAS;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GANE3C,OAAO,CAAC/C,EAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACN,CAAC,EAGDK,aAAa,iBACZ5I,OAAA;YAAKgI,SAAS,EAAC,uBAAuB;YAAAS,QAAA,gBACpCzI,OAAA;cAAKgI,SAAS,EAAC,6CAA6C;cAACsB,IAAI,EAAC,QAAQ;cAAAb,QAAA,eACxEzI,OAAA;gBAAMgI,SAAS,EAAC,iBAAiB;gBAAAS,QAAA,EAAC;cAAwB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNvI,OAAA;cAAMgI,SAAS,EAAC,uBAAuB;cAAAS,QAAA,EAAC;YAAwB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,gBACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAC;IAAEJ,IAAI;IAAEK,KAAK;IAAEP,OAAO;IAAEzF,OAAO;IAAEiG;EAAO,CAAC,kBAC7D3J,OAAA;IACEgI,SAAS,EAAE,cAActE,OAAO,GAAG,aAAa,GAAG,YAAY,EAAG;IAClEyF,OAAO,EAAEA,OAAQ;IACjBjB,KAAK,EAAEyB,MAAM,GAAG;MAAE,GAAGvE,iBAAiB;MAAEE,WAAW,EAAE;IAAE,CAAC,GAAGF,iBAAkB;IAAAqD,QAAA,eAE7EzI,OAAA;MAAKgI,SAAS,EAAC,kDAAkD;MAAAS,QAAA,gBAC/DzI,OAAA,CAACV,IAAI;QAAC+J,IAAI,EAAEA,IAAK;QAACnB,KAAK,EAAE;UAAC0B,QAAQ,EAAE;QAAQ;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChDmB,KAAK,iBAAI1J,OAAA;QAAMgI,SAAS,EAAC,MAAM;QAACE,KAAK,EAAE;UAAC0B,QAAQ,EAAE;QAAQ,CAAE;QAAAnB,QAAA,EAAEiB;MAAK;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CACT;EAED,oBACEvI,OAAA;IAAKgI,SAAS,EAAC,gBAAgB;IAAAS,QAAA,gBAC7BzI,OAAA;MAAAyI,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACRvI,OAAA;MAAKgI,SAAS,EAAC,4BAA4B;MAAAS,QAAA,eACzCzI,OAAA;QAAKgI,SAAS,EAAC,UAAU;QAAAS,QAAA,gBAEvBzI,OAAA;UAAKgI,SAAS,EAAC,wDAAwD;UAAAS,QAAA,gBACrEzI,OAAA;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXvI,OAAA;YACEgI,SAAS,EAAC,2BAA2B;YACrCmB,OAAO,EAAEpC,iBAAkB;YAC3BmB,KAAK,EAAE;cACL2B,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAE;YACFC,YAAY,EAAG3D,CAAC,IAAKA,CAAC,CAAC4D,aAAa,CAAChC,KAAK,CAAChD,eAAe,GAAG,SAAU;YACvEiF,YAAY,EAAG7D,CAAC,IAAKA,CAAC,CAAC4D,aAAa,CAAChC,KAAK,CAAChD,eAAe,GAAG,aAAc;YAAAuD,QAAA,gBAE3EzI,OAAA;cAAKgI,SAAS,EAAC,eAAe;cAAAS,QAAA,gBAC5BzI,OAAA;gBAAIgI,SAAS,EAAC,MAAM;gBAAAS,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCvI,OAAA;gBAAOgI,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAA+B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNvI,OAAA;cAAK+H,GAAG,EAAEvI,cAAe;cAACwI,SAAS,EAAC,gBAAgB;cAACC,GAAG,EAAC,SAAS;cAACC,KAAK,EAAE;gBAACL,KAAK,EAAE,MAAM;gBAAEgB,MAAM,EAAE;cAAM;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvI,OAAA,CAACP,QAAQ;UAAC2K,YAAY,EAAEvD;QAAiB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAG3CjH,cAAc,iBACbtB,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAACE,KAAK,EAAE;YAChCmC,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE,oBAAoB;YAC5BpF,eAAe,EAAE;UACnB,CAAE;UAAAuD,QAAA,eACAzI,OAAA;YAAKgI,SAAS,EAAC,4BAA4B;YAAAS,QAAA,gBACzCzI,OAAA;cAAKgI,SAAS,EAAC,kCAAkC;cAACsB,IAAI,EAAC,QAAQ;cAAAb,QAAA,eAC7DzI,OAAA;gBAAMgI,SAAS,EAAC,iBAAiB;gBAAAS,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNvI,OAAA;cAAIgI,SAAS,EAAC,mBAAmB;cAAAS,QAAA,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DvI,OAAA;cAAGgI,SAAS,EAAC,iBAAiB;cAAAS,QAAA,EAAC;YAAyC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA/H,OAAO,gBACNR,OAAA;UAAKgI,SAAS,EAAC,kBAAkB;UAAAS,QAAA,gBAC/BzI,OAAA;YAAKgI,SAAS,EAAC,gBAAgB;YAACsB,IAAI,EAAC,QAAQ;YAAAb,QAAA,eAC3CzI,OAAA;cAAMgI,SAAS,EAAC,iBAAiB;cAAAS,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNvI,OAAA;YAAGgI,SAAS,EAAC,iBAAiB;YAAAS,QAAA,EAAC;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,GACJjI,KAAK,CAACiK,MAAM,KAAK,CAAC,gBACpBvK,OAAA;UAAKgI,SAAS,EAAC,kBAAkB;UAAAS,QAAA,gBAC/BzI,OAAA,CAACV,IAAI;YAAC+J,IAAI,EAAC,kBAAkB;YAACnB,KAAK,EAAE;cAAE0B,QAAQ,EAAE,MAAM;cAAEY,KAAK,EAAE;YAAU;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EvI,OAAA;YAAGgI,SAAS,EAAC,iBAAiB;YAAAS,QAAA,EAAC;UAA8C;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,gBAENvI,OAAA,CAAAE,SAAA;UAAAuI,QAAA,GAEGnI,KAAK,CAACqC,GAAG,CAAC,CAACC,IAAI,EAAE6H,KAAK,kBACrBzK,OAAA;YAEEgI,SAAS,EAAC,WAAW;YACrBE,KAAK,EAAE;cACLmC,SAAS,EAAEI,KAAK,KAAK,CAAC,IAAI,CAACnJ,cAAc,GAAG,2BAA2B,GAAG,MAAM;cAChFoJ,SAAS,EAAED,KAAK,KAAK,CAAC,IAAI,CAACnJ,cAAc,GAAG,eAAe,GAAG;YAChE,CAAE;YAAAmH,QAAA,eAEFzI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAS,QAAA,gBAExBzI,OAAA;gBAAKgI,SAAS,EAAC,gCAAgC;gBAAAS,QAAA,gBAC7CzI,OAAA;kBAAK+H,GAAG,EAAEnF,IAAI,CAACE,IAAI,CAACG,MAAO;kBAAC+E,SAAS,EAAC,qBAAqB;kBAACC,GAAG,EAAErF,IAAI,CAACE,IAAI,CAACC,IAAK;kBAACmF,KAAK,EAAE;oBAACL,KAAK,EAAE,MAAM;oBAAEgB,MAAM,EAAE;kBAAM;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3HvI,OAAA;kBAAKgI,SAAS,EAAC,aAAa;kBAAAS,QAAA,gBAC1BzI,OAAA;oBAAIgI,SAAS,EAAC,MAAM;oBAAAS,QAAA,EAAE7F,IAAI,CAACE,IAAI,CAACC;kBAAI;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CvI,OAAA;oBAAOgI,SAAS,EAAC,YAAY;oBAAAS,QAAA,EAAE,IAAI1C,IAAI,CAACnD,IAAI,CAACqB,UAAU,CAAC,CAACgC,kBAAkB,CAAC;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvI,OAAA;gBAAKgI,SAAS,EAAC,MAAM;gBAAAS,QAAA,GAClBC,iBAAiB,CAAC9F,IAAI,CAACO,OAAO,CAAC,EAC/BwE,WAAW,CAAC/E,IAAI,CAACS,KAAK,CAAC;cAAA;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGNvI,OAAA;gBAAKgI,SAAS,EAAC,gCAAgC;gBAAAS,QAAA,gBAC7CzI,OAAA,CAACyJ,YAAY;kBACXJ,IAAI,EAAEzG,IAAI,CAACc,OAAO,GAAG,WAAW,GAAG,mBAAoB;kBACvDgG,KAAK,EAAE9G,IAAI,CAACgB,KAAM;kBAClBuF,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC3C,IAAI,CAACC,EAAE,CAAE;kBACnCa,OAAO,EAAEd,IAAI,CAACc;gBAAQ;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFvI,OAAA,CAACyJ,YAAY;kBACXJ,IAAI,EAAC,qBAAqB;kBAC1BK,KAAK,EAAE9G,IAAI,CAACmB,aAAa,IAAI,CAAE;kBAC/BoF,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACtD,IAAI,CAACC,EAAE;gBAAE;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACFvI,OAAA,CAACyJ,YAAY;kBACXJ,IAAI,EAAC,2BAA2B;kBAChCF,OAAO,EAAEA,CAAA,KAAMnC,WAAW,CAACpE,IAAI,CAAE;kBACjC+G,MAAM,EAAE;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLI,cAAc,CAAC/F,IAAI,CAAC;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC,GA7CD3F,IAAI,CAACC,EAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CT,CACN,CAAC,EAGDvH,oBAAoB,iBACnBhB,OAAA;YAAKgI,SAAS,EAAC,kBAAkB;YAACE,KAAK,EAAE;cACvCmC,SAAS,EAAE;YACb,CAAE;YAAA5B,QAAA,gBACAzI,OAAA;cAAKgI,SAAS,EAAC,kCAAkC;cAACsB,IAAI,EAAC,QAAQ;cAAAb,QAAA,eAC7DzI,OAAA;gBAAMgI,SAAS,EAAC,iBAAiB;gBAAAS,QAAA,EAAC;cAAqB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNvI,OAAA;cAAGgI,SAAS,EAAC,mBAAmB;cAAAS,QAAA,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN,EAEA,CAACvH,oBAAoB,IAAI,CAACN,WAAW,IAAII,OAAO,iBAC/Cd,OAAA;YAAKgI,SAAS,EAAC,kBAAkB;YAAAS,QAAA,eAC/BzI,OAAA;cACEgI,SAAS,EAAC,yBAAyB;cACnCmB,OAAO,EAAE5E,aAAc;cAAAkE,QAAA,gBAEvBzI,OAAA,CAACV,IAAI;gBAAC+J,IAAI,EAAC,kBAAkB;gBAACrB,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEA,CAACzH,OAAO,IAAIR,KAAK,CAACiK,MAAM,GAAG,CAAC,iBAC3BvK,OAAA;YAAKgI,SAAS,EAAC,kBAAkB;YAAAS,QAAA,eAC/BzI,OAAA;cAAGgI,SAAS,EAAC,YAAY;cAAAS,QAAA,EAAC;YAAmC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACN;QAAA,eAED,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnI,EAAA,CArnBID,IAAI;EAAA,QACSZ,WAAW;AAAA;AAAAoL,EAAA,GADxBxK,IAAI;AAunBV,eAAeA,IAAI;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}