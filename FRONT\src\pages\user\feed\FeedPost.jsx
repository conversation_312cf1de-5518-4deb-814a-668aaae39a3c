import React, { useState, useRef, useCallback, useMemo } from 'react'
import { Icon } from '@iconify/react'
import DefaultProfile from '../../../assets/images/profile/default-profile.png'
import { createPost } from '../../../services/feedServices'
import { toast } from 'react-toastify'

// Move MediaUploadButton outside to prevent recreation on every render
const MediaUploadButton = React.memo(({ icon, text, onClick, buttonStyle }) => (
  <button className="btn border text-muted btn-sm" style={buttonStyle} onClick={onClick} type="button">
    <Icon icon={icon} className="me-1 d-none d-md-inline" />
    <Icon icon={icon} className="d-md-none" />
    <span className="d-none d-md-inline">{text}</span>
  </button>
));

const FeedPost = ({ onPostSubmit, userProfile }) => {
  const [newPost, setNewPost] = useState('');
  const [newPostMedia, setNewPostMedia] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const MAX_CHARACTERS = 1000;

  // Refs for file inputs
  const imageInputRef = useRef(null);
  const videoInputRef = useRef(null);

  // Memoized styles to prevent re-renders
  const buttonStyle = useMemo(() => ({
    backgroundColor: 'transparent',
    borderColor: '#dee2e6'
  }), []);

  const postButtonStyle = useMemo(() => ({
    borderRadius: '20px',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    minWidth: '100px'
  }), []);

  // Event handlers
  const handleMediaUpload = useCallback((e, type) => {
    const file = e.target.files[0];
    if (file) {
      setNewPostMedia({
        type,
        url: URL.createObjectURL(file),
        file
      });
    }
  }, []);

  const handleImageUpload = useCallback((e) => {
    handleMediaUpload(e, 'image');
  }, [handleMediaUpload]);

  const handleVideoUpload = useCallback((e) => {
    handleMediaUpload(e, 'video');
  }, [handleMediaUpload]);

  const handleImageButtonClick = useCallback(() => {
    imageInputRef.current?.click();
  }, []);

  const handleVideoButtonClick = useCallback(() => {
    videoInputRef.current?.click();
  }, []);

  // Memoized textarea change handler
  const handleTextareaChange = useCallback((e) => {
    setNewPost(e.target.value);
  }, []);

  // Memoized computed values
  const hasContent = useMemo(() => newPost.trim() || newPostMedia, [newPost, newPostMedia]);
  const isPostButtonEnabled = useMemo(() => hasContent && !isSubmitting, [hasContent, isSubmitting]);
  const postButtonClass = useMemo(() =>
    `btn px-4 w-auto py-2 ${isPostButtonEnabled ? 'btn-primary' : 'btn-secondary'}`,
    [isPostButtonEnabled]
  );

  const handleSubmitPost = async () => {
    if (!newPost.trim() && !newPostMedia) {
      toast.error('Please add some content or media to your post');
      return;
    }

    if (newPost.length > MAX_CHARACTERS) {
      toast.error(`Post content cannot exceed ${MAX_CHARACTERS} characters`);
      return;
    }

    setIsSubmitting(true);
    try {
      const postData = {
        description: newPost.trim(),
        media: newPostMedia
      };

      const response = await createPost(postData);

      if (response.success) {
        toast.success('Post created successfully!');
        setNewPost('');
        setNewPostMedia(null);

        // Call the parent callback to refresh the feed
        if (onPostSubmit) {
          onPostSubmit(response.data.post);
        }
      } else {
        toast.error(response.data?.error_msg || 'Failed to create post');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      toast.error('Failed to create post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render functions
  const renderMedia = (media) => {
    if (!media) return null;

    const mediaStyle = { width: '100%', maxHeight: '400px' };

    if (media.type === 'image') {
      return <img src={media.url} className="img-fluid rounded" alt="Post media" style={{...mediaStyle, objectFit: 'cover'}} />;
    } else if (media.type === 'video') {
      return (
        <video className="img-fluid rounded" controls style={mediaStyle}>
          <source src={media.url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      );
    }
    return null;
  };



  return (
    <div className="card mb-4">
      <div className="card-body">
        <div className="d-flex mb-3">
          <img 
            src={userProfile?.profile_pic_url || DefaultProfile} 
            className="rounded-circle me-3" 
            alt={userProfile?.name || "Profile"} 
            style={{width: '40px', height: '40px'}} 
          />
          <div className="flex-grow-1">
            <textarea
              className="form-control border-0"
              rows="3"
              placeholder="What's on your mind?"
              value={newPost}
              onChange={handleTextareaChange}
              maxLength={MAX_CHARACTERS}
            />
            <div className="d-flex justify-content-end mt-2">
              <small className={newPost.length > MAX_CHARACTERS * 0.9 ? 'text-warning' : 'text-muted'}>
                {newPost.length}/{MAX_CHARACTERS} characters
              </small>
            </div>
          </div>
        </div>

        {/* Media Preview */}
        {newPostMedia && (
          <div className="mb-3">
            <div className="position-relative">
              {renderMedia(newPostMedia)}
              <button 
                className="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 w-auto"
                onClick={() => setNewPostMedia(null)}
              >
                <Icon icon="mdi:close" />
              </button>
            </div>
          </div>
        )}

        {/* Hidden file inputs */}
        <input
          ref={imageInputRef}
          type="file"
          accept="image/*"
          className="d-none"
          onChange={handleImageUpload}
        />
        <input
          ref={videoInputRef}
          type="file"
          accept="video/*"
          className="d-none"
          onChange={handleVideoUpload}
        />

        {/* Action Buttons */}
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex gap-2">
            <MediaUploadButton
              icon="mdi:camera"
              text="Photo"
              onClick={handleImageButtonClick}
              buttonStyle={buttonStyle}
            />
            <MediaUploadButton
              icon="mdi:video"
              text="Video"
              onClick={handleVideoButtonClick}
              buttonStyle={buttonStyle}
            />
          </div>
          <button
            className={postButtonClass}
            onClick={handleSubmitPost}
            disabled={!isPostButtonEnabled}
            style={postButtonStyle}
          >
            {isSubmitting ? (
              <>
                <div className="spinner-border spinner-border-sm me-2" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                Posting...
              </>
            ) : (
              <>
                <Icon icon="mdi:send" className="me-2" />
                Post
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeedPost;
