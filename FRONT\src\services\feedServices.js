import { GET, POST, UPLOAD_FILE, PUT, DELETE } from './apiController';

const endpoint = "/feed";

// Create a new post
export const createPost = async (postData) => {
  const formData = new FormData();

  if (postData.description) {
    formData.append('description', postData.description);
  }

  if (postData.media && postData.media.file) {
    formData.append('media', postData.media.file);
  }

  formData.append('domain', window.location.origin);

  return UPLOAD_FILE(endpoint + "/create_post", formData);
};

// Edit an existing post
export const editPost = async (postId, postData) => {
  const formData = new FormData();

  if (postData.description) {
    formData.append('description', postData.description);
  }

  if (postData.media && postData.media.file) {
    formData.append('media', postData.media.file);
  }

  formData.append('domain', window.location.origin);

  return UPLOAD_FILE(endpoint + `/edit_post/${postId}`, formData, 'PUT');
};

// Delete a post
export const deletePost = async (postId) => {
  return DELETE(endpoint + `/delete_post/${postId}`, {
    domain: window.location.origin
  });
};

// Get all feeds with pagination
export const getAllFeeds = async (page = 1, limit = 5) => {
  return POST(endpoint + "/get_all_feeds", { 
    page, 
    limit,
    domain: window.location.origin 
  });
};

// Toggle like on a post
export const toggleLike = async (postId) => {
  return POST(endpoint + `/toggle_like/${postId}`, {
    domain: window.location.origin
  });
};

// Add a comment to a post
export const addComment = async (postId, comment) => {
  return POST(endpoint + `/add_comment/${postId}`, { 
    comment,
    domain: window.location.origin 
  });
};

// Edit a comment
export const editComment = async (commentId, comment) => {
  return PUT(endpoint + `/edit_comment/${commentId}`, { 
    comment,
    domain: window.location.origin 
  });
};

// Delete a comment
export const deleteComment = async (commentId) => {
  return DELETE(endpoint + `/delete_comment/${commentId}`, {
    domain: window.location.origin
  });
};

// Get comments for a specific post
export const getPostComments = async (postId, page = 1, limit = 10) => {
  return GET(endpoint + `/get_comments/${postId}?page=${page}&limit=${limit}`);
};

// Get user's own posts (for MyFeed component)
export const getMyPosts = async (page = 1, limit = 5) => {
  return POST(endpoint + "/get_all_feeds", { 
    page, 
    limit, 
    user_only: true,
    domain: window.location.origin 
  });
};

// Generate shareable URL for a post
export const generatePostShareUrl = async (postId) => {
  return POST(endpoint + "/generate_share_url", {
    post_id: postId,
    domain: window.location.origin
  });
};

// Get public post details (no authentication required)
export const getPublicPostDetails = async (data) => {
  return POST("/feed/get_public_post_details", data);
};