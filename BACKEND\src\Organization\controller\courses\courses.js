const { response } = require("express");
const bcrypt = require("bcrypt");
const axios = require("axios");
const { mysqlServerConnection } = require("../../../db/db");
const createdb = require("../../../superAdmin/db/utils/createOrganizationDb");
const {
  Validate,
  hashPassword,
  generateAccessToken,
  generateRefreshToken,
} = require("../../../tools/tools");
const { body } = require("express-validator");
const fs = require("fs");
const path = require("path");
const { GenerateCertificate } = require("../certificates/certificate");
const {
  uploadDocToS3,
  uploadVideoToS3,
  uploadImageToS3,
} = require("../../../tools/aws");
const { encodeData, decodeData } = require("../../../utils/encodeAndEncode");

// const { getVideoDurationInSeconds } = require('get-video-duration');


const mainDbName = process.env.MAIN_DB;

const { getVideoDurationInSeconds } = require("get-video-duration");
const { stringify } = require("querystring");

const updateCourse = async (req, res) => {
  try {
    const courseId = req.params.courseId;
    const file = req.file;
    const created_by = req.user.userId;

    console.log("Updating course ID:", courseId);
    console.log("Request body:", req.body);

    let banner = null;

    // Upload new banner if provided
    if (file) {
      console.log("Uploading new banner image...");
      const s3Response = await uploadImageToS3(file.buffer);
      banner = s3Response.path;
      console.log("New banner uploaded:", banner);
    } else {
      // Use existing banner if not updated
      const [existingCourse] = await mysqlServerConnection.query(
        `SELECT banner_image FROM ${req.user.db_name}.courses WHERE id = ?`,
        [courseId]
      );
      banner = existingCourse?.[0]?.banner_image || null;
      console.log("Using existing banner:", banner);
    }

    // Extract and validate form data
    const {
      courseTitle,
      courseDescription,
      courseLevel,
      courseLanguage,
      courseCategory,
      certificateType,
      courseType,
      currency,
      price,
      discountPrice,
      tags,
      courseInfo,
    } = req.body;

    // Validate required fields
    if (
      !courseTitle ||
      !courseDescription ||
      !courseLevel ||
      !courseLanguage ||
      !courseCategory
    ) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg:
            "Required fields are missing: courseTitle, courseDescription, courseLevel, courseLanguage, courseCategory",
        },
      });
    }

    // Parse JSON fields
    let parsedTags = [];
    let parsedCourseInfo = [];

    try {
      parsedTags =
        typeof tags === "string"
          ? JSON.parse(tags)
          : Array.isArray(tags)
            ? tags
            : [];
    } catch (error) {
      console.warn("Error parsing tags:", error);
      parsedTags = [];
    }

    try {
      parsedCourseInfo =
        typeof courseInfo === "string"
          ? JSON.parse(courseInfo)
          : Array.isArray(courseInfo)
            ? courseInfo
            : [];
    } catch (error) {
      console.warn("Error parsing courseInfo:", error);
      parsedCourseInfo = [];
    }

    console.log("Parsed tags:", parsedTags);
    console.log("Parsed courseInfo:", parsedCourseInfo);

    // Update query with all the new fields
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.courses
         SET created_by = ?,
             banner_image = ?,
             course_name = ?,
             course_desc = ?,
             course_price = ?,
             discount_price = ?,
             currency = ?,
             course_type = ?,
             course_category = ?,
             levels = ?,
             course_language = ?,
             certificate_template_id = ?,
             tags = ?,
             course_info = ?,
             updatedAt = NOW()
         WHERE id = ?`,
      [
        created_by,
        banner,
        courseTitle,
        courseDescription,
        price,
        discountPrice || "0",
        currency || "USD",
        courseType || "free",
        courseCategory,
        courseLevel,
        courseLanguage,
        certificateType || null,
        JSON.stringify(parsedTags),
        JSON.stringify(parsedCourseInfo),
        courseId,
      ]
    );

    console.log("Course updated successfully");

    res.status(200).json({
      success: true,
      data: {
        message: "Course updated successfully.",
        courseId: courseId,
      },
    });
  } catch (error) {
    console.error("Error updating course:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error: " + error.message },
    });
  }
};

const showAllCourses = async (req, res) => {
  try {
    const { page = 1, limit = 10, search } = req.body;
    const pageNum = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const offset = (pageNum - 1) * pageSize;
    const db = req.user.db_name;
    const userId = req.user.userId;

    console.log("req.user full object:", JSON.stringify(req.user, null, 2));
    console.log("userId", userId);
    // Build WHERE clauses
    const whereClauses = [
      `c.is_active = 1`,
      `c.is_deleted = 0`,
      `c.is_approved = 1`,
      `NOT EXISTS (
       SELECT 1
       FROM ${db}.mycourses myc
       WHERE myc.course_id = c.id
         AND myc.user_id = ?
    )`,
    ];

    const whereParams = [userId];

    // optional search
    if (search && search.trim()) {
      whereClauses.push(`(c.course_name LIKE ? OR c.course_desc LIKE ?)`);
      const likeTerm = `%${search.trim()}%`;
      whereParams.push(likeTerm, likeTerm);
    }

    const whereSQL = `WHERE ${whereClauses.join(" AND ")}`;

    // paginated data query
    const dataSQL = `
        SELECT
          c.*
        FROM ${db}.courses c
        ${whereSQL}
        ORDER BY c.course_type, c.course_name
        LIMIT ? OFFSET ?
      `;
    const dataParams = [...whereParams, pageSize, offset];

    // count query (same filters, no pagination)
    const countSQL = `
        SELECT COUNT(*) AS total
        FROM ${db}.courses c
        ${whereSQL}
      `;
    const countParams = [...whereParams];

    // run queries
    const [courses] = await mysqlServerConnection.query(dataSQL, dataParams);
    const [[{ total }]] = await mysqlServerConnection.query(
      countSQL,
      countParams
    );

    console.log("Number of Courses:", total, courses);
    const totalPages = total > 0 ? Math.ceil(total / pageSize) : 1;

    if (!courses.length) {
      return res.json({
        success: true,
        data: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          courses: [],
          metadata: [],
        },
      });
    }

    const metadata = await Promise.all(
      courses.map((c) => getCourseMetadata(c.id, db))
    );

    res.json({
      success: true,
      data: {
        currentPage: pageNum,
        totalPages,
        totalItems: total,
        courses,
        metadata,
      },
    });
  } catch (err) {
    console.error("Error fetching courses:", err);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};



const getCourseForLandingPage = async (req, res) => {
  console.log("🚀 getCourseForLandingPage - API CALLED");
  console.log("📋 Full request body:", req.body);
  console.log("📋 Request headers:", req.headers);
  console.log("📋 Request method:", req.method);
  console.log("📋 Request URL:", req.url);
  
  try {
    const { dbName, dbname } = req.body;

    console.log("🔍 dbName value:", dbName);
    console.log("🔍 dbname value:", dbname);
    
    let db;
    
    // If dbName or dbname is provided, use it directly (accept both cases)
    if (dbName || dbname) {
      db = dbName || dbname;
      console.log("✅ Database selected:", db);
    }
    // If neither is provided, return error
    else {
      console.log("❌ No database name provided");
      return res.status(400).json({
        success: false,
        data: { error_msg: "dbName or dbname is required in request body." },
      });
    }

    console.log("🔧 Building SQL query...");
    
    // Updated WHERE clause: Only fetch active, non-deleted, approved courses
    const whereClauses = [
      `c.is_active = 1`,
      `c.is_deleted = 0`,
      `c.is_approved = 1`
    ];

    const whereSQL = `WHERE ${whereClauses.join(" AND ")}`;
    console.log("🔧 WHERE clause:", whereSQL);

    // Query to fetch all matching courses
    const dataSQL = `
      SELECT c.*
      FROM ${db}.courses c
      ${whereSQL}
      ORDER BY c.course_type, c.course_name
    `;

    console.log("🔧 Final SQL query:", dataSQL);
    console.log("🔧 Executing database query...");

    const [courses] = await mysqlServerConnection.query(dataSQL);
    
    console.log("📊 Database query completed");
    console.log("📊 Number of courses found:", courses.length);

    // Total items and pages (even though there's no pagination now)
    const total = courses.length;
    const totalPages = 1;
    const currentPage = 1;

    console.log("📊 Total courses:", total);
    console.log("📊 Total pages:", totalPages);
    console.log("📊 Current page:", currentPage);

    if (total === 0) {
      console.log("⚠️ No courses found - returning empty response");
      return res.json({
        success: true,
        data: {
          currentPage,
          totalPages,
          totalItems: total,
          courses: [],
          metadata: [],
        },
      });
    }

    console.log("🔧 Fetching metadata for courses...");
    const metadata = await Promise.all(
      courses.map((c) => getCourseMetadata(c.id, db))
    );
    console.log("📊 Metadata fetched for", metadata.length, "courses");

    console.log("✅ Preparing final response...");
    console.log("✅ Response data structure:", {
      currentPage,
      totalPages,
      totalItems: total,
      coursesCount: courses.length,
      metadataCount: metadata.length
    });

    return res.json({
      success: true,
      data: {
        currentPage,
        totalPages,
        totalItems: total,
        courses,
        metadata,
      },
    });
  } catch (err) {
    console.error("❌ Error in getCourseForLandingPage:", err);
    console.error("❌ Error stack:", err.stack);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};


const getMyAllCourses = async (req, res) => {
  try {
    const result = req.body;
    const page = parseInt(result.page) || 1;
    const limit = parseInt(result.limit) || 10;
    const offset = (page - 1) * limit;
    const searchTerm = result.search ? `%${result.search}%` : null;

    console.log("Request body---------------------------:", result);

    let baseQuery = `
      SELECT mc.*, 
             c.course_name, 
             c.course_desc, 
             c.course_category, 
             c.course_subcategory, 
             c.banner_image, 
             c.course_price, 
             c.total_rating,
             c.levels,
             c.course_url
      FROM ${req.user.db_name}.mycourses mc
      LEFT JOIN ${req.user.db_name}.courses c ON mc.course_id = c.id
      WHERE mc.user_id = ? 
        AND c.is_deleted = 0 
        AND c.is_active = 1 
        AND c.is_approved = 1
    `;

    let countQuery = `
      SELECT COUNT(*) as total 
      FROM ${req.user.db_name}.mycourses mc 
      LEFT JOIN ${req.user.db_name}.courses c ON mc.course_id = c.id 
      WHERE mc.user_id = ? 
        AND c.is_deleted = 0 
        AND c.is_active = 1 
        AND c.is_approved = 1
    `;

    let queryParams = [req.user.userId];

    if (searchTerm) {
      const searchCondition = ` AND (
        c.course_name LIKE ? OR 
        c.course_desc LIKE ? OR 
        c.course_category LIKE ? OR 
        c.course_subcategory LIKE ?
      )`;
      baseQuery += searchCondition;
      countQuery += searchCondition;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    baseQuery += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    const [courses] = await mysqlServerConnection.query(baseQuery, queryParams);

    // Generate metadata for each course: total_modules and totalUsers
    const metadata = await Promise.all(
      courses.map(async (course) => {
        const [[{ total_modules }]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_modules 
           FROM ${req.user.db_name}.modules 
           WHERE course_id = ? AND is_deleted = 0 AND is_active = 1`,
          [course.course_id]
        );

        const [[{ totalUsers }]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS totalUsers 
           FROM ${req.user.db_name}.mycourses 
           WHERE course_id = ?`,
          [course.course_id]
        );

        return {
          course_id: course.course_id,
          total_modules: total_modules || 0,
          totalUsers: totalUsers || 0,
        };
      })
    );

    const countParams = searchTerm
      ? [req.user.userId, searchTerm, searchTerm, searchTerm, searchTerm]
      : [req.user.userId];

    const [[{ total }]] = await mysqlServerConnection.query(
      countQuery,
      countParams
    );

    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        currentPage: page,
        totalPages: totalPages,
        totalItems: total,
        courses: courses,
        metadata: metadata,
      },
    });
  } catch (error) {
    console.error("Error fetching courses:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

const createCourseModule = async (req, res) => {
  try {
    const result = req.body;
    console.log(
      "Result :",
      result.course_id,
      result.module_desc,
      result.module_name
    );

    if (Object.keys(result).length == 0) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: "Following fields are required",
          response: {
            course_id: "",
            module_desc: "",
            module_name: "",
            module_sequence: "",
          },
        },
      });
    } else {
      await mysqlServerConnection.query(
        `INSERT INTO ${req.user.db_name}.modules (
            course_id,
            module_desc,
            module_name,
            module_sequence
            ) VALUES (?,?,?,?)`,
        [
          result.course_id,
          result.module_desc,
          result.module_name,
          result.module_sequence,
        ]
      );

      res.status(201).json({
        success: true,
        data: {
          message: "Successfully created",
          response: result,
        },
      });
    }
  } catch (error) {
    console.error("Error creating organization:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const timeStringToSeconds = (timeString) => {
  if (!timeString) {
    return 0;
  }
  const [minutes, seconds] = timeString.split(":").map(Number);
  return (minutes || 0) * 60 + (seconds || 0);
};

const getModule = async (req, res) => {
  try {
    const { course_id } = req.body;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Course ID is required." },
      });
    }

    const [modules] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.modules WHERE course_id = ? AND is_deleted = 0 AND is_active = 1`,
      [course_id]
    );

    if (modules.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "No modules found for the provided course ID." },
      });
    }

    const modulesWithContent = await Promise.all(
      modules.map(async (module) => {
        const [videos] = await mysqlServerConnection.query(
          `SELECT 
            v.id, 
            v.module_id, 
            'video' AS type, 
            v.video_name AS title, 
            v.video_url AS url, 
            v.video_description AS description, 
            v.video_duration AS duration, 
            v.createdAt, 
            v.serial_no, 
            COALESCE(rv.is_complete, 0) AS completed, 
            COALESCE(rv.user_id, 0) AS recent_user_id 
          FROM ${req.user.db_name}.videos v
          LEFT JOIN ${req.user.db_name}.recent_videos rv
            ON v.id = rv.video_id AND rv.user_id = ?
          WHERE v.module_id = ? AND v.is_deleted = 0 AND v.is_active = 1`,
          [req.user.userId, module.id]
        );

        const [documents] = await mysqlServerConnection.query(
          `SELECT id, module_id, 'document' AS type, doc_name AS title, doc_url AS url, createdAt, serial_no
           FROM ${req.user.db_name}.documents 
           WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
          [module.id]
        );

        const [assessments] = await mysqlServerConnection.query(
          `SELECT
            a.id,
            a.module_id,
            a.assessment_name AS title,
            'assessment' AS type,
            NULL AS url,
            NULL AS duration,
            a.createdAt,
            a.serial_no,
            COALESCE(au.is_completed, 0) AS completed
           FROM ${req.user.db_name}.assessments a
           LEFT JOIN ${req.user.db_name}.assessment_users au
             ON a.id = au.assessment_id AND au.user_id = ?
           WHERE a.module_id = ?
             AND a.is_active = 1
             AND a.is_deleted = 0
             AND a.assessment_type = 'assessment'
             AND a.is_approved = 1`,
          [req.user.userId, module.id]
        );

        // ✅ Updated Survey Logic
        const [surveys] = await mysqlServerConnection.query(
          `SELECT 
            a.id, 
            a.module_id, 
            a.assessment_name AS title, 
            'survey' AS type, 
            NULL AS url, 
            NULL AS duration, 
            a.createdAt, 
            a.serial_no, 
            CASE 
              WHEN COALESCE(au.is_completed, 0) = 1 THEN 1
              WHEN EXISTS (
                SELECT 1 FROM ${req.user.db_name}.question_answer qa
                WHERE qa.assessment_id = a.id AND qa.user_id = ?
                LIMIT 1
              ) THEN 1
              ELSE 0
            END AS completed
          FROM ${req.user.db_name}.assessments a
          LEFT JOIN ${req.user.db_name}.assessment_users au 
            ON a.id = au.assessment_id AND au.user_id = ?
          WHERE a.module_id = ? 
            AND a.is_active = 1 
            AND a.is_deleted = 0
            AND a.assessment_type = 'survey' 
            AND a.is_approved = 1`,
          [req.user.userId, req.user.userId, module.id]
        );

        const [assignments] = await mysqlServerConnection.query(
          `SELECT 
            a.id, 
            a.module_id, 
            a.assignment_name AS title, 
            'assignment' AS type, 
            NULL AS url, 
            NULL AS duration, 
            a.createdAt, 
            a.serial_no,
            au.is_completed as completed
           FROM ${req.user.db_name}.assignments a
           LEFT JOIN ${req.user.db_name}.assignment_user au 
             ON a.id = au.assignment_id
           WHERE a.module_id = ? AND a.is_active = 1 AND au.user_id = ?`,
          [module.id, req.user.userId]
        );

        const combinedContent = [
          ...videos,
          ...documents,
          ...assessments,
          ...assignments,
          ...surveys,
        ];

        const sortedContent = combinedContent
          .map((item) => ({
            ...item,
            serial_no: parseInt(item.serial_no, 10) || 0,
          }))
          .sort((a, b) => a.serial_no - b.serial_no);

        return {
          ...module,
          content: sortedContent,
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        message:
          "Successfully fetched module content by course ID in ascending time order",
        response: modulesWithContent,
      },
    });
  } catch (error) {
    console.error("Error fetching module content:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

const getModulesList = async (req, res) => {
  try {
    const { course_id } = req.params;
    const { search } = req.query;
    const db = req.user.db_name;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Course ID is required." },
      });
    }

    // 🔄 Step 1: Fetch course_name (only one field)
    const [[courseRow]] = await mysqlServerConnection.query(
      `SELECT course_name FROM ${db}.courses WHERE id = ? AND is_deleted = 0`,
      [course_id]
    );

    const course_name = courseRow?.course_name || null;

    // 🔄 Step 2: Fetch modules
    let query = `
      SELECT id, course_id, module_name, module_desc, createdAt, is_active
      FROM ${db}.modules
      WHERE course_id = ? AND is_deleted = 0
    `;
    const queryParams = [course_id];

    if (search) {
      query += ` AND (LOWER(module_name) LIKE LOWER(?) OR LOWER(module_desc) LIKE LOWER(?))`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    const [modules] = await mysqlServerConnection.query(query, queryParams);

    if (modules.length === 0) {
      return res.status(200).json({
        success: false,
        course_name,

        data: {
          message: search
            ? "No modules found matching your search criteria."
            : "No modules found for the provided course ID.",
          response: [],
        },
      });
    }

    // Step 3: Enhance each module with content counts
    const modulesWithDetails = await Promise.all(
      modules.map(async (module) => {
        const moduleId = module.id;

        const [[videoCounts]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_videos
           FROM ${db}.videos
           WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
          [moduleId]
        );

        const [[assessmentCounts]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_assessments
           FROM ${db}.assessments
           WHERE module_id = ? AND assessment_type = 'assessment' AND is_deleted = 0 AND is_active = 1`,
          [moduleId]
        );

        const [[surveyCounts]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_assessments
           FROM ${db}.assessments
           WHERE module_id = ? AND assessment_type = 'survey' AND is_deleted = 0 AND is_active = 1`,
          [moduleId]
        );

        const [[assignmentCounts]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_assignments
           FROM ${db}.assignments
           WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
          [moduleId]
        );

        const [[documentCounts]] = await mysqlServerConnection.query(
          `SELECT COUNT(*) AS total_documents
           FROM ${db}.documents
           WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
          [moduleId]
        );

        const [[lastSerials]] = await mysqlServerConnection.query(
          `SELECT MAX(CAST(serial_no AS UNSIGNED)) AS last_serial_no FROM (
              SELECT serial_no FROM ${db}.videos WHERE module_id = ? AND is_deleted = 0 AND is_active = 1
              UNION ALL
              SELECT serial_no FROM ${db}.assessments WHERE module_id = ? AND is_deleted = 0 AND is_active = 1
              UNION ALL
              SELECT serial_no FROM ${db}.assignments WHERE module_id = ? AND is_deleted = 0 AND is_active = 1
              UNION ALL
              SELECT serial_no FROM ${db}.documents WHERE module_id = ? AND is_deleted = 0 AND is_active = 1
          ) AS all_serials`,
          [moduleId, moduleId, moduleId, moduleId]
        );

        return {
          ...module,
          video_counts: videoCounts.total_videos || 0,
          assessment_counts: assessmentCounts.total_assessments || 0,
          survey_counts: surveyCounts.total_assessments || 0,
          assignment_counts: assignmentCounts.total_assignments || 0,
          document_counts: documentCounts.total_documents || 0,
          last_serial_no: lastSerials?.last_serial_no || 0,
        };
      })
    );

    // Step 4: Send final response with course_name included
    res.status(200).json({
      success: true,
      course_name,
      message: "Successfully fetched modules list with content details.",
      data: {
        modulesWithDetails,
      },
    });
  } catch (error) {
    console.error("❌ Error fetching modules list:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

const getModuleContent = async (req, res) => {
  try {
    const { module_id } = req.params;

    if (!module_id) {
      return res.status(400).json({
        success: false,
        course_name: null,
        data: { error_msg: "Module ID is required." },
      });
    }

    const db = req.user.db_name;

    // Step 0: Get course_id from module
    const [[moduleRow]] = await mysqlServerConnection.query(
      `SELECT course_id FROM ${db}.modules WHERE id = ? AND is_deleted = 0`,
      [module_id]
    );

    const course_id = moduleRow?.course_id;

    // Step 0.1: Get course_name from course_id
    let course_name = null;
    if (course_id) {
      const [[courseRow]] = await mysqlServerConnection.query(
        `SELECT course_name FROM ${db}.courses WHERE id = ? AND is_deleted = 0`,
        [course_id]
      );
      course_name = courseRow?.course_name || null;
    }

    // --- VIDEOS ---
    const [videos] = await mysqlServerConnection.query(
      `
      SELECT 
        v.id, v.course_id, v.module_id, 'video' AS type,
        v.video_name AS title,
        v.video_url AS url,
        v.video_description AS description,
        v.video_duration AS duration,
        v.is_active, v.createdAt, v.serial_no,
        vd.attachment_detail,
        (
          SELECT COUNT(*) FROM ${db}.messagebox
          WHERE video_id = v.id AND module_id = v.module_id
        ) AS total_comments
      FROM ${db}.videos v
      LEFT JOIN ${db}.video_details vd ON v.id = vd.video_id
      WHERE v.module_id = ? AND v.is_deleted = 0
      `,
      [module_id]
    );

    // --- DOCUMENTS ---
    const [documents] = await mysqlServerConnection.query(
      `
      SELECT 
        d.id, d.course_id, d.module_id, 'document' AS type,
        d.doc_name AS title,
        d.doc_url AS url,
        d.is_active, d.createdAt, d.serial_no,
        ROUND(LENGTH(d.doc_url) / (1024 * 1024), 2) AS total_size_mb
      FROM ${db}.documents d
      WHERE d.module_id = ? AND d.is_deleted = 0
      `,
      [module_id]
    );

    // --- ASSESSMENTS ---
    const [assessments] = await mysqlServerConnection.query(
      `
      SELECT
        a.id, a.course_id, a.module_id,
        a.assessment_name AS title,
        a.assessment_type AS type,
        NULL AS url,
        a.is_active,
        a.earn_point,
        a.earn_point_applicable_after,
        a.duration,
        a.createdAt,
        a.serial_no,
        a.pass_percentage,
        (
          SELECT COUNT(*) FROM ${db}.questions q
          WHERE q.assessment_id = a.id
        ) AS total_questions
      FROM ${db}.assessments a
      WHERE a.module_id = ? AND a.is_deleted = 0
      `,
      [module_id]
    );

    // --- ASSIGNMENTS ---
    const [assignments] = await mysqlServerConnection.query(
      `
      SELECT 
        id, course_id, module_id,
        assignment_name AS title,
        'assignment' AS type,
        NULL AS url,
        is_active,
        NULL AS duration,
        createdAt,
        serial_no
      FROM ${db}.assignments
      WHERE module_id = ? AND is_deleted = 0
      `,
      [module_id]
    );

    // --- Combine & Sort by serial_no ---
    const combinedContent = [
      ...videos,
      ...documents,
      ...assessments,
      ...assignments,
    ]
      .map((item) => ({
        ...item,
        serial_no: parseInt(item.serial_no, 10) || 0,
      }))
      .sort((a, b) => a.serial_no - b.serial_no);

    return res.status(200).json({
      success: true,
      message: "Successfully fetched module content.",
      course_name,
      data: {
        response: combinedContent,
      },
    });
  } catch (error) {
    console.error("❌ Error fetching module content:", error);
    return res.status(500).json({
      success: false,
      course_name: null,
      data: { error_msg: "Internal server error." },
    });
  }
};

const addVideoAndDetails = async (req, res, next) => {
  console.log("addVideoAndDetails-------------");
  try {
    console.log("Request body:", req.body);
    console.log("Request files:", req.files);
    console.log("Request headers:", req.headers);

    const result = req.body;
    const file = req.files;

    if (Object.keys(result).length === 0) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: "Following fields are required",
          response: {
            video: "file",
            video_name: "",
            video_desc: "",
            attachment_link: "",
            attachment_file: "file",
            module_id: "",
            total_content: "",
          },
        },
      });
    }

    const [course] = await mysqlServerConnection.query(
      `SELECT course_id FROM ${req.user.db_name}.modules WHERE id = ?`,
      [result.module_id]
    );

    if (course.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "No course found for the provided module ID." },
      });
    }

    const course_id = course[0].course_id;

    const [courseCheck] = await mysqlServerConnection.query(
      `SELECT id FROM ${req.user.db_name}.courses WHERE id = ?`,
      [course_id]
    );

    if (courseCheck.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg:
            "The referenced course ID does not exist in the courses table.",
        },
      });
    }

    let path = { path: "" };

    // ✅ Handle video URL source based on upload_view_type
    if (result.upload_view_type === "src") {
      if (!result.src || result.src.trim() === "") {
        return res.status(400).json({
          success: false,
          data: {
            error_msg: "src is required when upload_view_type is 'src'",
          },
        });
      }
      path.path = result.src;
    } else {
      // Default: upload to S3
      console.log("Files received:", file);
      console.log("Video file check:", file?.video);

      // Check if video file exists
      if (!file || !file.video || !file.video[0]) {
        return res.status(400).json({
          success: false,
          data: {
            error_msg:
              "Video file is required when upload_view_type is 'uploaded'",
          },
        });
      }

      console.log("Video file details:", {
        originalname: file.video[0].originalname,
        mimetype: file.video[0].mimetype,
        size: file.video[0].size,
      });

      path = await uploadVideoToS3(
        file.video[0].buffer,
        file.video[0].originalname
      );
    }

    console.log("Video duration received:", result.duration);

    // Get next serial_no
    const [maxSerialNoResult] = await mysqlServerConnection.query(
      `SELECT MAX(serial_no) as max_serial_no FROM (
        SELECT serial_no FROM ${req.user.db_name}.videos WHERE module_id = ? AND is_deleted = 0
        UNION ALL
        SELECT serial_no FROM ${req.user.db_name}.assignments WHERE module_id = ? AND is_deleted = 0
        UNION ALL
        SELECT serial_no FROM ${req.user.db_name}.assessments WHERE module_id = ? AND is_deleted = 0
        UNION ALL
        SELECT serial_no FROM ${req.user.db_name}.documents WHERE module_id = ? AND is_deleted = 0
      ) AS all_content`,
      [result.module_id, result.module_id, result.module_id, result.module_id]
    );

    const nextSerialNo = maxSerialNoResult[0].max_serial_no
      ? parseInt(maxSerialNoResult[0].max_serial_no) + 1
      : 1;

    // Insert video
    const [videos] = await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.videos (
        course_id,
        module_id,
        video_url,
        video_name,
        upload_view_type,
        video_title,
        video_description,
        serial_no,
        video_duration
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        course_id,
        result.module_id,
        path.path,
        result.video_name,
        result.upload_view_type || "uploaded",
        result.video_title || "",
        result.video_desc,
        nextSerialNo,
        result.duration,
      ]
    );

    // Insert video attachment details
    await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.video_details (
        video_id,
        attachment_type,
        attachment_detail
      ) VALUES (?, ?, ?)`,
      [
        videos.insertId,
        result.attachment_link ? "link" : "file",
        result.attachment_link ? result.attachment_link : path.path,
      ]
    );

    // Success
    res.status(201).json({
      success: true,
      data: {
        message: "Course video added successfully",
        response: result,
      },
    });
  } catch (error) {
    console.error("Error adding video and details:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const bulkUploadVideos = async (req, res, next) => {
  try {
    const data = req.body;
    const files = req.files;
    console.log(files, "=============");

    if (!data || data.length === 0) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: "No videos provided for bulk upload.",
        },
      });
    }

    const dbName = req.user.db_name;
    const [course] = await mysqlServerConnection.query(
      `SELECT course_id FROM ${dbName}.modules WHERE id = ?`,
      [data.module_id]
    );

    if (course.length === 0) {
      throw new Error(`No course found for the module ID: ${video.module_id}`);
    }

    const course_id = course[0].course_id;
    const insertVideoPromises = files.file.map(async (video, index) => {
      console.log(video.file, "----------------");

      const [courseCheck] = await mysqlServerConnection.query(
        `SELECT id FROM ${dbName}.courses WHERE id = ?`,
        [course_id]
      );

      const [videoInsert] = await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.videos
                (
                    course_id,
                    module_id,
                    video_url,
                    video_name,
                    video_description,
                    serial_no,
                    video_duration
                ) VALUES (?,?,?,?,?,?,?)`,
        [
          course_id,
          data.module_id,
          "/uploads/" + video.filename,
          video.originalname,
          "No description provided.",
          data.total_content + index,
          null, // Add the duration parameter
        ]
      );

      await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.video_details
                (
                    video_id,
                    attachment_type,
                    attachment_detail
                ) VALUES (?,?,?)`,
        [videoInsert.insertId, "link", "/uploads/" + video.filename]
      );

      return {
        video_id: videoInsert.insertId,
        video_name: video.originalname,
        status: "Uploaded",
      };
    });

    const uploadResults = await Promise.all(insertVideoPromises);

    res.status(201).json({
      success: true,
      data: {
        message: "Videos uploaded successfully.",
        response: uploadResults,
      },
    });
  } catch (error) {
    console.error("Error during bulk video upload:", error);
    next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const addToMyCourse = async (req, res) => {
  try {
    const { course_id } = req.query;
    console.log("course_id-----------------------------", course_id);

    // Check if the course exists
    const [courseRows] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.courses WHERE id = ? LIMIT 1`,
      [course_id]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg:
            "The referenced course ID does not exist in the courses table.",
        },
      });
    }

    const course = courseRows[0]; // Extract the course data

    // Check if the course is already purchased by the user
    const [isCourseExistRows] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.mycourses WHERE course_id = ? AND user_id = ? LIMIT 1`,
      [course_id, req.user.userId]
    );

    if (isCourseExistRows.length > 0) {
      return res.status(404).json({
        // 409 Conflict is more appropriate here
        success: false,
        data: { error_msg: "Course already added to My Courses" },
      });
    }

    // Check if the course is free or if the payment is confirmed
    if (
      course.course_price > 0 &&
      course.course_type &&
      course.course_type.toLowerCase() === "paid"
    ) {
      // Here you would check the payment status, for example:
      const paymentStatus = await checkUserPaymentStatus(
        req.user.userId,
        course_id
      ); // You need to implement this function

      if (!paymentStatus) {
        return res.status(402).json({
          // 402 Payment Required
          success: false,
          data: { error_msg: "Payment required for this course." },
        });
      }
    }

    // If course is free or payment is successful, add the course to the user's purchased courses
    await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.mycourses (user_id, course_id) VALUES (?, ?)`,
      [req.user.userId, course_id]
    );

    // Update the subscribers count for the course
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.courses SET subscribers = subscribers + 1 WHERE id = ?`,
      [course_id]
    );

    // Respond with success
    res.status(201).json({
      success: true,
      data: {
        message: "Successfully added course to My Courses",
        course_id: course_id,
      },
    });
  } catch (error) {
    console.error("Error adding course:", error.message);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const checkCoursePurchase = async (req, res, next) => {
  console.log("📡 === checkCoursePurchase API Called ===");

  try {
    const { user_id, course_id } = req.body;
    const dbname = req.user.db_name;

    if (!user_id || !course_id) {
      return res.status(400).json({
        success: false,
        message: "Both user_id and course_id are required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbname}.mycourses 
         WHERE user_id = ? AND course_id = ? LIMIT 1`,
      [user_id, course_id]
    );

    const isPurchased = result.length > 0;

    return res.status(200).json({
      success: true,
      message: isPurchased
        ? "Course is purchased by the user"
        : "Course is not purchased by the user",
      data: {
        isPurchased,
      },
    });
  } catch (error) {
    console.error("❌ Error in checkCoursePurchase:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// Example function to check payment status
const checkUserPaymentStatus = async (userId, courseId) => {
  // Implement the logic to check if the user has paid for the course
  // This could involve checking a payments table, an external payment API, etc.
  return true; // Assume true for now, replace with actual logic
};

const getCoursePassFailStats = async (req, res) => {
  try {
    const { course_id } = req.params;
    const db_name = req.user.db_name;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    const query = `
                SELECT
                    COUNT(*) AS total_purchased,
                    SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) AS completed,
                    SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) AS in_progress
                FROM ${db_name}.mycourses
                WHERE course_id = ?
            `;

    const [result] = await mysqlServerConnection.query(query, [course_id]);
    console.log("result", result);

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching course stats:", error);
    return res.status(500).json({
      success: false,
      message: "Internal Server Error",
    });
  }

  // try {
  //     const { course_id } = req.params;
  //     console.log("course_id", course_id)
  //     const db_name = req.user.db_name

  //     if (!course_id) {
  //         return res.status(400).json({
  //             success: false,
  //             message: "Course ID are required"
  //         });
  //     }

  //     const query = `
  //         SELECT
  //             COUNT(DISTINCT CASE WHEN au.is_completed = true THEN au.user_id END) as passed_count,
  //             COUNT(DISTINCT CASE WHEN au.is_completed = false THEN au.user_id END) as failed_count,
  //             COUNT(DISTINCT mc.user_id) - COUNT(DISTINCT au.user_id) as not_attempted_count
  //         FROM ${db_name}.mycourses mc
  //         LEFT JOIN ${db_name}.assessments a ON a.course_id = mc.course_id
  //         LEFT JOIN ${db_name}.assessment_users au ON au.assessment_id = a.id AND au.user_id = mc.user_id
  //         WHERE mc.course_id = ?
  //         GROUP BY mc.course_id
  //     `;

  //     const [results] = await mysqlServerConnection.query(query, [course_id]);

  //     if (!results || results.length === 0) {
  //         return res.status(404).json({
  //             success: false,
  //             message: "No data found for this course"
  //         });
  //     }

  //     const stats = results[0];
  //     const total = stats.passed_count + stats.failed_count + stats.not_attempted_count;

  //     return res.status(200).json({
  //         success: true,
  //         data: {
  //             passed: {
  //                 count: stats.passed_count,
  //                 percentage: ((stats.passed_count / total) * 100).toFixed(2)
  //             },
  //             failed: {
  //                 count: stats.failed_count,
  //                 percentage: ((stats.failed_count / total) * 100).toFixed(2)
  //             },
  //             not_attempted: {
  //                 count: stats.not_attempted_count,
  //                 percentage: ((stats.not_attempted_count / total) * 100).toFixed(2)
  //             },
  //             total
  //         }
  //     });

  // } catch (error) {
  //     console.error('Error in getCoursePassFailStats:', error);
  //     return res.status(500).json({
  //         success: false,
  //         message: "Internal server error",
  //         error: error.message
  //     });
  // }
};

const getRecentVideos = async (req, res) => {
  try {
    // SQL query to fetch the most recent video details for a specific user along with video information
    const query = `
            SELECT 
                rv.id AS recent_video_id,
                rv.user_id,
                rv.video_id,
                v.id AS video_id,
                v.course_id,
                v.video_name,
                v.video_url,
                v.video_title,
                v.video_description,
                rv.completed_stream,
                rv.createdAt AS watched_at,
                rv.updatedAt AS last_updated
            FROM ${req.user.db_name}.recent_videos rv
            JOIN ${req.user.db_name}.users u ON rv.user_id = u.id
            JOIN ${req.user.db_name}.videos v ON rv.video_id = v.id
            WHERE rv.user_id = ?
            ORDER BY rv.updatedAt DESC
            LIMIT 1;
        `;

    // Execute the query to fetch the most recent video for the specific user
    const [recentVideos] = await mysqlServerConnection.query(query, [
      req.user.userId,
    ]);

    // Check if any recent videos were found
    if (recentVideos.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "No recent videos found for this user",
        },
      });
    }

    // Format the response to include the recent video data
    const formattedRecentVideos = recentVideos.map((video) => ({
      recent_video_id: video.recent_video_id,
      user_id: video.user_id,
      video_id: video.video_id,
      video_name: video.video_name,
      video_url: video.video_url,
      video_title: video.video_title,
      video_description: video.video_description,
      completed_stream: video.completed_stream,
      watched_at: video.watched_at,
      last_updated: video.last_updated,
      course_id: video.course_id,
    }));

    return res.status(200).json({
      success: true,
      data: {
        recent_videos: formattedRecentVideos,
      },
    });
  } catch (error) {
    console.error("Error retrieving recent videos:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const saveRecentVideo = async (req, res) => {
  try {
    const { video_detail_id, completed_stream } = req.body;
    console.log(
      req.user.userId,
      video_detail_id,
      completed_stream,
      "++++++++++++++++++++++++"
    );

    // Check if the video_detail_id exists in the videos table and get its duration
    const checkVideoQuery = `SELECT id, video_duration FROM ${req.user.db_name}.videos WHERE id = ?`;
    const [videoExists] = await mysqlServerConnection.query(checkVideoQuery, [
      video_detail_id,
    ]);

    // If the video doesn't exist, return an error
    if (!videoExists || videoExists.length === 0) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Invalid video_detail_id. The video does not exist.",
        },
      });
    }

    // Parse video duration and completed stream from mm:ss to seconds
    const videoDuration = timeStringToSeconds(videoExists[0].video_duration);
    const completedStreamSeconds = timeStringToSeconds(completed_stream);

    // Calculate completion percentage
    const completionPercentage = (completedStreamSeconds / videoDuration) * 100;
    const isComplete = completionPercentage >= 60;

    // Check if this video for the user is already marked complete
    const checkCompletionQuery = `SELECT is_complete FROM ${req.user.db_name}.recent_videos WHERE user_id = ? AND video_id = ?`;
    const [existingRecord] = await mysqlServerConnection.query(
      checkCompletionQuery,
      [req.user.userId, video_detail_id]
    );

    // Set is_complete to true only if it is not already true in the database
    const finalIsComplete =
      existingRecord && existingRecord.is_complete ? true : isComplete;

    // SQL query to insert or update recent video details
    const query = `
  INSERT INTO ${req.user.db_name}.recent_videos (user_id, video_id, completed_stream, is_complete)
  VALUES (?, ?, ?, 1)
  ON DUPLICATE KEY UPDATE
    completed_stream = VALUES(completed_stream),
    is_complete = 1,
    updatedAt = CURRENT_TIMESTAMP;
`;

    // Execute the query with the calculated is_complete status
    await mysqlServerConnection.query(query, [
      req.user.userId,
      video_detail_id,
      completed_stream,
      finalIsComplete,
    ]);

    console.log("working");
    return res.status(200).json({
      success: true,
      data: {
        message: "Recent video details saved successfully.",
      },
    });
  } catch (error) {
    console.error("Error saving recent video details:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const getCourseCompletionPercentage = async (req, res) => {
  console.log("==== getCourseCompletionPercentage START ====");
  console.log("[PARAMS]", req.params);
  console.log("[USER]", req.user);

  const { video_id, course_id } = req.params;
  console.log("[COURSE ID]", course_id);
  console.log("[VIDEO ID]", video_id);
  let course;

  try {
    if (!course_id || course_id === "null") {
      console.log("[INFO] course_id is null, fetching from video_id...");
      const query = `SELECT course_id FROM ${req.user.db_name}.videos WHERE id = ? AND is_active = 1`;
      console.log("[QUERY]", query, [video_id]);

      [course] = await mysqlServerConnection.query(query, [video_id]);

      console.log("[RESULT] course from video_id:", course);
    }

    const resolvedCourseId =
      course_id !== "null" ? course_id : course[0]?.course_id;
    console.log("[RESOLVED] Course ID:", resolvedCourseId);

    console.log("[QUERY] Fetching total videos...");
    const [videoCount] = await mysqlServerConnection.query(
      `SELECT count(*) as videoCount 
       FROM ${req.user.db_name}.videos 
       WHERE course_id = ? AND is_active = 1 AND is_deleted = 0`,
      [resolvedCourseId]
    );
    console.log("[RESULT] Total Videos:", videoCount);

    console.log("[QUERY] Fetching completed videos...");
    const [completedVideos] = await mysqlServerConnection.query(
      `SELECT count(*) as completed_video_count
   FROM ${req.user.db_name}.recent_videos rv 
   JOIN ${req.user.db_name}.videos v ON rv.video_id = v.id 
   WHERE
     rv.user_id = ?
     AND v.course_id = ? 
     AND rv.is_complete = 1 
     AND v.is_active = 1`,
      [req.user.userId, resolvedCourseId]
    );

    console.log("[RESULT] Completed Videos:", completedVideos);

    const totalVideos = videoCount[0]?.videoCount || 0;
    const completedVideosCount = completedVideos[0]?.completed_video_count || 0;

    console.log("[QUERY] Fetching total assessments...");
    const [assessmentCount] = await mysqlServerConnection.query(
      `SELECT count(*) as assessmentCount
       FROM ${req.user.db_name}.assessments 
       WHERE course_id = ? AND is_active = 1 AND is_deleted = 0`,
      [resolvedCourseId]
    );
    console.log("[RESULT] Total Assessments:", assessmentCount);

    console.log("[QUERY] Fetching completed assessments...");
    const [completedAssessments] = await mysqlServerConnection.query(
      `SELECT count(*) as completed_assessment_count
       FROM ${req.user.db_name}.assessment_users au
       JOIN ${req.user.db_name}.assessments a ON au.assessment_id = a.id
       WHERE a.course_id = ? 
       AND au.user_id = ? 
       AND au.is_completed = 1 
       AND a.is_active = 1
       AND a.is_deleted = 0`,
      [resolvedCourseId, req.user.userId]
    );
    console.log("[RESULT] Completed Assessments:", completedAssessments);

    const totalAssessments = assessmentCount[0]?.assessmentCount || 0;
    const completedAssessmentsCount =
      completedAssessments[0]?.completed_assessment_count || 0;

    const totalContent = totalVideos + totalAssessments;
    const completedContent = completedVideosCount + completedAssessmentsCount;

    const videoCompletionPercentage =
      totalVideos > 0 ? (completedVideosCount / totalVideos) * 100 : 0;
    const assessmentCompletionPercentage =
      totalAssessments > 0
        ? (completedAssessmentsCount / totalAssessments) * 100
        : 0;
    const overallCompletionPercentage =
      totalContent > 0 ? (completedContent / totalContent) * 100 : 0;

    console.log("[CALCULATED] Total Content:", totalContent);
    console.log("[CALCULATED] Completed Content:", completedContent);
    console.log("[PERCENTAGE] Video:", videoCompletionPercentage.toFixed(2));
    console.log(
      "[PERCENTAGE] Assessment:",
      assessmentCompletionPercentage.toFixed(2)
    );
    console.log(
      "[PERCENTAGE] Overall:",
      overallCompletionPercentage.toFixed(2)
    );

    if (overallCompletionPercentage >= 75) {
      console.log("[CONDITION] Completion >= 75%, updating mycourses...");
      const updateQuery = `UPDATE ${req.user.db_name}.mycourses SET is_completed = 1 WHERE course_id = ? AND user_id = ?`;
      console.log("[QUERY]", updateQuery, [resolvedCourseId, req.user.userId]);

      await mysqlServerConnection.query(updateQuery, [
        resolvedCourseId,
        req.user.userId,
      ]);
      console.log("[UPDATED] mycourses marked as completed.");
    }

    res.status(200).json({
      success: true,
      data: {
        totalVideos,
        completedVideos: completedVideosCount,
        videoCompletionPercentage: videoCompletionPercentage.toFixed(2),
        totalAssessments,
        completedAssessments: completedAssessmentsCount,
        assessmentCompletionPercentage:
          assessmentCompletionPercentage.toFixed(2),
        totalContent,
        completedContent,
        overallCompletionPercentage: overallCompletionPercentage.toFixed(2),
      },
    });

    console.log("==== getCourseCompletionPercentage END ====");
  } catch (error) {
    console.error("[ERROR]", error.message);
    res.status(500).json({
      success: false,
      error: error.message || "Internal Server Error",
    });
  }
};

const getCourseMetadata = async (course_id, db_name) => {
  try {
    // console.log("COURSE ID",course_id);
    const [totalModules] = await mysqlServerConnection.query(
      `
        SELECT count(*) as mod_count from ${db_name}.modules 
        where course_id = ?`,
      [course_id]
    );

    const [userCount] = await mysqlServerConnection.query(
      `SELECT count(*) as userCount from ${db_name}.mycourses 
        where course_id = ?`,
      [course_id]
    );

    const [duration] = await mysqlServerConnection.query(
      `
        SELECT SEC_TO_TIME(SUM(TIME_TO_SEC(video_duration))) AS total_hours
        FROM ${db_name}.videos where course_id = ?`,
      [course_id]
    );

    // console.log("COURSE METADATA:",totalModules,userCount,duration);

    return {
      totalModules: totalModules[0].mod_count,
      totalUsers: userCount[0].userCount,
      duration: duration[0].total_hours,
    };
  } catch (error) {
    console.log("EEEERRRRORRR", error.message);
  }
};

const fetchCoursesByClassroom = async (req, res) => {
  try {
    const { class_id } = req.params;
    const [courses] = await mysqlServerConnection.query(
      `
        Select c.* from ${req.user.db_name}.courses c join ${req.user.db_name}.mycourses mc
        on c.id = mc.course_id where mc.class_id = ?
        and mc.user_id = ?`,
      [class_id, req.user.userId]
    );

    const metadata = await Promise.all(
      courses.map(async (course) => {
        const data = await getCourseMetadata(course.id, req.user.db_name);
        return data;
      })
    );

    res.status(200).json({
      courses,
      metadata,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

const updateSerialNumber = async (req, res) => {
  try {
    const { module_id, firstSerialNo, secondSerialNo } = req.body;

    if (
      !module_id ||
      firstSerialNo === undefined ||
      secondSerialNo === undefined
    ) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Module ID and serial numbers are required." },
      });
    }

    const dbName = req.user.db_name;

    // Fetch items corresponding to the provided serial numbers
    const [firstItem] = await mysqlServerConnection.query(
      `SELECT id, 'videos' AS table_name FROM ${dbName}.videos WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'documents' AS table_name FROM ${dbName}.documents WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'assessments' AS table_name FROM ${dbName}.assessments WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'assignments' AS table_name FROM ${dbName}.assignments WHERE module_id = ? AND serial_no = ?`,
      [
        module_id,
        firstSerialNo,
        module_id,
        firstSerialNo,
        module_id,
        firstSerialNo,
        module_id,
        firstSerialNo,
      ]
    );

    const [secondItem] = await mysqlServerConnection.query(
      `SELECT id, 'videos' AS table_name FROM ${dbName}.videos WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'documents' AS table_name FROM ${dbName}.documents WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'assessments' AS table_name FROM ${dbName}.assessments WHERE module_id = ? AND serial_no = ?
            UNION ALL
            SELECT id, 'assignments' AS table_name FROM ${dbName}.assignments WHERE module_id = ? AND serial_no = ?`,
      [
        module_id,
        secondSerialNo,
        module_id,
        secondSerialNo,
        module_id,
        secondSerialNo,
        module_id,
        secondSerialNo,
      ]
    );

    if (!firstItem || !secondItem) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "One or both items not found." },
      });
    }

    console.log(secondItem[0].table_name, "-------------------");
    // Swap the serial numbers
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.${firstItem[0].table_name} SET serial_no = ? WHERE id = ?`,
      [secondSerialNo, firstItem[0].id]
    );

    await mysqlServerConnection.query(
      `UPDATE ${dbName}.${secondItem[0].table_name} SET serial_no = ? WHERE id = ?`,
      [firstSerialNo, secondItem[0].id]
    );

    res.status(200).json({
      success: true,
      data: { message: "Serial numbers swapped successfully." },
    });
  } catch (error) {
    console.error("Error updating serial numbers:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const updateContentOrder = async (req, res) => {
  try {
    const { module_id, ordered_items } = req.body;

    if (!module_id || !ordered_items || !Array.isArray(ordered_items)) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Module ID and ordered items array are required." },
      });
    }

    const dbName = req.user.db_name;

    // Update serial numbers for each item
    for (let i = 0; i < ordered_items.length; i++) {
      const item = ordered_items[i];
      const newSerialNo = i + 1; // Serial numbers start from 1

      await mysqlServerConnection.query(
        `UPDATE ${dbName}.${item.table_name} SET serial_no = ? WHERE id = ?`,
        [newSerialNo, item.id]
      );
    }

    res.status(200).json({
      success: true,
      data: {
        message: "Content order updated successfully.",
        updated_items: ordered_items.length,
      },
    });
  } catch (error) {
    console.error("Error updating content order:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const getUserAssessmentDetails = async (req, res) => {
  try {
    const { course_id } = req.params;
    const { search = "", page = 1, limit = 10, status } = req.query;
    const db_name = req.user.db_name;
    const offset = (page - 1) * limit;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    let statusCondition = "";
    if (status === "passed") {
      statusCondition = `AND au.is_completed = 1`;
    } else if (status === "failed") {
      statusCondition = `AND au.is_completed = 0`;
    }

    const baseQuery = `
          SELECT 
    u.id AS user_id,
    u.profile_pic_url,
    u.name AS user_name,
    u.email,
    CASE
        WHEN COUNT(au.id) = 0 THEN 'Not Attempted'
        WHEN SUM(au.is_completed) = COUNT(a.id) THEN 'Passed'
        ELSE 'Failed'
    END AS overall_status,
    CASE
        WHEN MAX(au.id) IS NULL THEN 'Not Attempted'
        WHEN MAX(au.is_completed) = 1 THEN 'Passed'
        ELSE 'Failed'
    END AS assessment_status
FROM ${db_name}.mycourses mc
JOIN ${db_name}.users u ON u.id = mc.user_id
LEFT JOIN ${db_name}.assessments a ON a.course_id = mc.course_id
LEFT JOIN ${db_name}.assessment_users au ON au.assessment_id = a.id AND au.user_id = mc.user_id
WHERE mc.course_id = ?
${statusCondition}
AND (u.name LIKE ? OR u.email LIKE ?)
GROUP BY u.id, u.profile_pic_url, u.name, u.email

        `;

    const countQuery = `
            SELECT COUNT(*) AS total
            FROM (
                ${baseQuery}
            ) AS user_statuses
        `;

    const query =
      baseQuery +
      `
            ORDER BY u.name
            LIMIT ? OFFSET ?
        `;

    const searchPattern = search === "" ? "%" : `%${search}%`;

    // Fetch users
    const [results] = await mysqlServerConnection.query(query, [
      course_id,
      searchPattern,
      searchPattern,
      parseInt(limit),
      parseInt(offset),
    ]);
    const [countResult] = await mysqlServerConnection.query(countQuery, [
      course_id,
      searchPattern,
      searchPattern,
    ]);

    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    // Fetch total course content (videos + assessments) once
    const [[{ videoCount }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS videoCount FROM ${db_name}.videos WHERE course_id = ? AND is_active = 1`,
      [course_id]
    );
    const [[{ assessmentCount }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS assessmentCount FROM ${db_name}.assessments WHERE course_id = ? AND is_active = 1`,
      [course_id]
    );
    const totalContent = videoCount + assessmentCount;

    // Compute completion percentage per user
    for (const user of results) {
      const [[{ completed_video_count }]] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS completed_video_count
                 FROM ${db_name}.recent_videos rv
                 JOIN ${db_name}.videos v ON rv.video_id = v.id
                 WHERE v.course_id = ? AND rv.user_id = ? AND rv.is_complete = 1 AND v.is_active = 1`,
        [course_id, user.user_id]
      );

      const [[{ completed_assessment_count }]] =
        await mysqlServerConnection.query(
          `SELECT COUNT(*) AS completed_assessment_count
                 FROM ${db_name}.assessment_users au
                 JOIN ${db_name}.assessments a ON au.assessment_id = a.id
                 WHERE a.course_id = ? AND au.user_id = ? AND au.is_completed = 1 AND a.is_active = 1`,
          [course_id, user.user_id]
        );

      const completedContent =
        completed_video_count + completed_assessment_count;
      const overallCompletionPercentage =
        totalContent > 0 ? (completedContent / totalContent) * 100 : 0;

      user.overallCompletionPercentage = overallCompletionPercentage.toFixed(2);
    }

    // console.log("User assessment details",results)

    return res.status(200).json({
      success: true,
      data: {
        users: results,
        pagination: {
          total_records: totalRecords,
          total_pages: totalPages,
          current_page: parseInt(page),
          limit: parseInt(limit),
        },
      },
    });
  } catch (error) {
    console.error("Error in getUserAssessmentDetails:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const CourseUserActivity = async (req, res, next) => {
  try {
    const { course_id } = req.params;

    // Ensure the course_id is provided
    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `
            SELECT 
                ul.id, 
                ul.user_id, 
                ul.log_name, 
                ul.log_description, 
                ul.file_url, 
                ul.createdAt, 
                u.name AS user_name, 
                u.email AS user_email 
            FROM ${req.user.db_name}.user_logs ul
            JOIN ${req.user.db_name}.mycourses mc ON ul.user_id = mc.user_id
            JOIN ${req.user.db_name}.users u ON u.id = ul.user_id
            WHERE mc.course_id = ?
            ORDER BY ul.createdAt DESC
        `,
      [course_id]
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error in userActivity:", error);
    return next({ statusCode: 500, message: error.message });
  }
};

const getSurveysByCourseId = async (req, res) => {
  try {
    const { course_id, search = "", page = 1, limit = 10 } = req.body;
    const db_name = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("📥 Incoming Request:", { course_id, search, page, limit });

    if (!course_id) {
      console.log("❌ Missing course_id");
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    const searchPattern = search === "" ? "%" : `%${search}%`;

    // ✅ FIXED QUERY - Include is_deleted filter to exclude deleted surveys
    const baseQuery = `
      SELECT
        a.id AS assessment_id,
        a.assessment_name,
        a.duration,
        a.earn_point,
        a.pass_percentage,
        a.is_active
      FROM ${db_name}.assessments a
      WHERE a.course_id = ?
        AND a.assessment_type = 'survey'
        AND a.is_deleted = 0
        AND (a.assessment_name LIKE ? OR CAST(a.pass_percentage AS CHAR) LIKE ?)
    `;

    const countQuery = `
      SELECT COUNT(*) AS total
      FROM (
        ${baseQuery}
      ) AS survey_count
    `;

    const query =
      baseQuery +
      `
      ORDER BY a.assessment_name
      LIMIT ? OFFSET ?
    `;

    const [results] = await mysqlServerConnection.query(query, [
      course_id,
      searchPattern,
      searchPattern,
      parseInt(limit),
      parseInt(offset),
    ]);

    console.log("✅ Fetched Surveys:", results.length);

    const [countResult] = await mysqlServerConnection.query(countQuery, [
      course_id,
      searchPattern,
      searchPattern,
    ]);

    // Early return if no surveys found
    if (results.length === 0) {
      console.log("ℹ️ No surveys found for course:", course_id);
      return res.status(200).json({
        success: true,
        data: {
          surveys: [],
          pagination: {
            total_records: 0,
            total_pages: 0,
            current_page: parseInt(page),
            limit: parseInt(limit),
          },
        },
      });
    }

    const assessmentIds = results.map((survey) => survey.assessment_id);
    console.log("🔍 Assessment IDs for sub-queries:", assessmentIds);

    // Query to get question counts - FIXED to directly count from questions table
    const questionCountQuery = `
      SELECT
        q.assessment_id,
        COUNT(q.id) AS question_count
      FROM ${db_name}.questions q
      WHERE q.assessment_id IN (?)
        AND q.is_deleted = 0
      GROUP BY q.assessment_id
    `;
    const [questionCountResult] = await mysqlServerConnection.query(
      questionCountQuery,
      [assessmentIds]
    );
    console.log("📊 Question count data:", questionCountResult);

    // Query to get student attempts - FIXED to properly count unique user attempts
    const studentAttemptsQuery = `
      SELECT
        qa.assessment_id,
        COUNT(DISTINCT qa.user_id) AS student_attempts
      FROM ${db_name}.question_answer qa
      JOIN ${db_name}.assessments a ON qa.assessment_id = a.id
      WHERE qa.assessment_id IN (?)
        AND qa.user_id IS NOT NULL
        AND a.assessment_type = 'survey'
        AND a.is_deleted = 0
      GROUP BY qa.assessment_id
    `;
    const [studentAttemptsResult] = await mysqlServerConnection.query(
      studentAttemptsQuery,
      [assessmentIds]
    );
    console.log("👥 Student attempt data:", studentAttemptsResult);

    // Merge data
    const surveysWithDetails = results.map((survey) => {
      const questionData = questionCountResult.find(
        (count) => count.assessment_id === survey.assessment_id
      );
      const attemptData = studentAttemptsResult.find(
        (attempt) => attempt.assessment_id === survey.assessment_id
      );
      return {
        ...survey,
        question_count: questionData ? questionData.question_count : 0,
        student_attempts: attemptData ? attemptData.student_attempts : 0,
      };
    });

    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    console.log("🚀 Returning surveys:", surveysWithDetails.length);

    return res.status(200).json({
      success: true,
      data: {
        surveys: surveysWithDetails,
        pagination: {
          total_records: totalRecords,
          total_pages: totalPages,
          current_page: parseInt(page),
          limit: parseInt(limit),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error fetching surveys:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const getAssessmentsByCourseId = async (req, res) => {
  try {
    const { course_id, search = "", page = 1, limit = 10 } = req.body;
    const db_name = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("📥 Incoming Request for Assessments:", {
      course_id,
      search,
      page,
      limit,
    });

    if (!course_id) {
      console.log("❌ Missing course_id");
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    const searchPattern = search === "" ? "%" : `%${search}%`;

    // Query for assessments (not surveys)
    const baseQuery = `
      SELECT
        a.id AS assessment_id,
        a.assessment_name,
        a.duration,
        a.earn_point,
        a.pass_percentage,
        a.is_active,
        a.createdAt
      FROM ${db_name}.assessments a
      WHERE a.course_id = ?
        AND a.assessment_type = 'assessment'
        AND (a.assessment_name LIKE ? OR CAST(a.pass_percentage AS CHAR) LIKE ?)
    `;

    const countQuery = `
      SELECT COUNT(*) AS total
      FROM (
        ${baseQuery}
      ) AS assessment_count
    `;

    const query =
      baseQuery +
      `
      ORDER BY a.assessment_name
      LIMIT ? OFFSET ?
    `;

    const [results] = await mysqlServerConnection.query(query, [
      course_id,
      searchPattern,
      searchPattern,
      parseInt(limit),
      parseInt(offset),
    ]);

    console.log("✅ Fetched Assessments:", results.length);

    const [countResult] = await mysqlServerConnection.query(countQuery, [
      course_id,
      searchPattern,
      searchPattern,
    ]);

    // Early return if no assessments found
    if (results.length === 0) {
      console.log("ℹ️ No assessments found for course:", course_id);
      return res.status(200).json({
        success: true,
        data: {
          assessments: [],
          pagination: {
            total_records: 0,
            total_pages: 0,
            current_page: parseInt(page),
            limit: parseInt(limit),
          },
        },
      });
    }

    // Get additional details for each assessment
    const assessmentsWithDetails = await Promise.all(
      results.map(async (assessment) => {
        try {
          // Get total questions count
          const [questionsCount] = await mysqlServerConnection.query(
            `SELECT COUNT(*) AS total_questions
             FROM ${db_name}.questions
             WHERE assessment_id = ? AND is_deleted = 0`,
            [assessment.assessment_id]
          );

          // Get total attempts count
          const [attemptsCount] = await mysqlServerConnection.query(
            `SELECT COUNT(DISTINCT user_id) AS total_attempts
             FROM ${db_name}.question_answer
             WHERE assessment_id = ?`,
            [assessment.assessment_id]
          );

          return {
            ...assessment,
            total_questions: questionsCount[0]?.total_questions || 0,
            total_attempts: attemptsCount[0]?.total_attempts || 0,
          };
        } catch (detailError) {
          console.error(
            `❌ Error fetching details for assessment ${assessment.assessment_id}:`,
            detailError
          );
          return {
            ...assessment,
            total_questions: 0,
            total_attempts: 0,
          };
        }
      })
    );

    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    console.log("🚀 Returning assessments:", assessmentsWithDetails.length);

    return res.status(200).json({
      success: true,
      data: {
        assessments: assessmentsWithDetails,
        pagination: {
          total_records: totalRecords,
          total_pages: totalPages,
          current_page: parseInt(page),
          limit: parseInt(limit),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error fetching assessments:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const getAssessmentDetails = async (req, res) => {
  try {
    const { assessment_id } = req.params;
    const db_name = req.user.db_name;

    console.log("📥 Fetching assessment details for ID:", assessment_id);

    if (!assessment_id) {
      return res.status(400).json({
        success: false,
        message: "Assessment ID is required",
      });
    }

    // Get basic assessment details
    const [assessmentDetails] = await mysqlServerConnection.query(
      `SELECT
        a.id AS assessment_id,
        a.assessment_name,
        a.duration,
        a.pass_percentage,
        a.earn_point,
        a.is_active,
        a.createdAt,
        c.course_name
      FROM ${db_name}.assessments a
      LEFT JOIN ${db_name}.courses c ON a.course_id = c.id
      WHERE a.id = ? AND a.assessment_type = 'assessment'`,
      [assessment_id]
    );

    if (assessmentDetails.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found",
      });
    }

    const assessment = assessmentDetails[0];

    // Get total questions count
    const [questionsCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS total_questions
       FROM ${db_name}.questions
       WHERE assessment_id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    // Get total unique attempts
    const [attemptsCount] = await mysqlServerConnection.query(
      `SELECT COUNT(DISTINCT user_id) AS total_attempts
       FROM ${db_name}.question_answer
       WHERE assessment_id = ?`,
      [assessment_id]
    );

    // Get average score and pass rate
    const [scoreStats] = await mysqlServerConnection.query(
      `SELECT
        AVG(
          (SELECT COUNT(*)
           FROM ${db_name}.question_answer qa2
           WHERE qa2.user_id = qa.user_id
             AND qa2.assessment_id = qa.assessment_id
             AND qa2.is_correct = 1) /
          (SELECT COUNT(*)
           FROM ${db_name}.questions q
           WHERE q.assessment_id = qa.assessment_id
             AND q.is_deleted = 0) * 100
        ) AS average_score,
        COUNT(DISTINCT qa.user_id) AS total_users,
        SUM(
          CASE WHEN (
            (SELECT COUNT(*)
             FROM ${db_name}.question_answer qa2
             WHERE qa2.user_id = qa.user_id
               AND qa2.assessment_id = qa.assessment_id
               AND qa2.is_correct = 1) /
            (SELECT COUNT(*)
             FROM ${db_name}.questions q
             WHERE q.assessment_id = qa.assessment_id
               AND q.is_deleted = 0) * 100
          ) >= ? THEN 1 ELSE 0 END
        ) AS passed_users
      FROM ${db_name}.question_answer qa
      WHERE qa.assessment_id = ?
      GROUP BY qa.assessment_id`,
      [assessment.pass_percentage, assessment_id]
    );

    // Calculate pass rate
    const stats = scoreStats[0] || {
      average_score: 0,
      total_users: 0,
      passed_users: 0,
    };
    const passRate =
      stats.total_users > 0
        ? (stats.passed_users / stats.total_users) * 100
        : 0;

    // Get recent attempts with user details
    const [recentAttempts] = await mysqlServerConnection.query(
      `SELECT DISTINCT
        u.id AS user_id,
        u.name AS user_name,
        u.email,
        u.profile_pic_url,
        (SELECT COUNT(*)
         FROM ${db_name}.question_answer qa2
         WHERE qa2.user_id = u.id
           AND qa2.assessment_id = ?
           AND qa2.is_correct = 1) AS correct_answers,
        (SELECT COUNT(*)
         FROM ${db_name}.questions q
         WHERE q.assessment_id = ?
           AND q.is_deleted = 0) AS total_questions,
        MAX(qa.createdAt) AS last_attempt_date
      FROM ${db_name}.question_answer qa
      JOIN ${db_name}.users u ON qa.user_id = u.id
      WHERE qa.assessment_id = ?
      GROUP BY u.id, u.name, u.email, u.profile_pic_url
      ORDER BY last_attempt_date DESC
      LIMIT 10`,
      [assessment_id, assessment_id, assessment_id]
    );

    // Calculate individual scores
    const attemptsWithScores = recentAttempts.map((attempt) => ({
      ...attempt,
      score_percentage:
        attempt.total_questions > 0
          ? ((attempt.correct_answers / attempt.total_questions) * 100).toFixed(
            2
          )
          : 0,
      passed:
        attempt.total_questions > 0
          ? (attempt.correct_answers / attempt.total_questions) * 100 >=
          assessment.pass_percentage
          : false,
    }));

    const result = {
      assessment: {
        ...assessment,
        total_questions: questionsCount[0]?.total_questions || 0,
        total_attempts: attemptsCount[0]?.total_attempts || 0,
        average_score: stats.average_score
          ? parseFloat(stats.average_score).toFixed(2)
          : 0,
        pass_rate: parseFloat(passRate).toFixed(2),
      },
      recent_attempts: attemptsWithScores,
    };

    console.log("✅ Assessment details fetched successfully");

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("❌ Error fetching assessment details:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};



const getSingleSurveyDetails = async (req, res) => {
  try {
    const { assessment_id } = req.body;
    const db_name = req.user.db_name;

    console.log("🔍 getSingleSurveyDetails called with:", {
      assessment_id,
      db_name,
    });

    if (!assessment_id) {
      console.log("❌ Missing assessment_id");
      return res.status(400).json({
        success: false,
        message: "Assessment ID is required",
      });
    }

    // First, verify the assessment exists and is a survey
    const assessmentCheckQuery = `
      SELECT id, assessment_name, assessment_type, is_deleted
      FROM ${db_name}.assessments
      WHERE id = ?
    `;

    const [assessmentCheck] = await mysqlServerConnection.query(
      assessmentCheckQuery,
      [assessment_id]
    );
    console.log("📋 Assessment check result:", assessmentCheck);

    if (assessmentCheck.length === 0) {
      console.log("❌ Assessment not found");
      return res.status(404).json({
        success: false,
        message: "Assessment not found",
      });
    }

    if (assessmentCheck[0].is_deleted === 1) {
      console.log("❌ Assessment is deleted");
      return res.status(404).json({
        success: false,
        message: "Assessment has been deleted",
      });
    }

    // Query to get all questions for the given assessment_id
    const questionsQuery = `
      SELECT id AS question_id, question, question_type, options
      FROM ${db_name}.questions
      WHERE assessment_id = ? AND is_deleted = 0
    `;

    // Fetch all questions related to the assessment_id
    const [questions] = await mysqlServerConnection.query(questionsQuery, [
      assessment_id,
    ]);

    console.log("📝 Questions found:", questions.length);

    // If no questions found, return empty but successful response
    if (questions.length === 0) {
      console.log("ℹ️ No questions found for this survey");
      return res.status(200).json({
        success: true,
        data: [],
        message: "No questions found for this survey",
      });
    }

    // Create a structure to hold question responses
    const surveyDetails = [];

    for (const question of questions) {
      // Query to get all answers for a particular question
      const answersQuery = `
        SELECT qa.id AS answer_id, qa.user_id, qa.answer, qa.is_correct
        FROM ${db_name}.question_answer qa
        WHERE qa.question_id = ?
      `;

      // Fetch all answers for the current question
      const [answers] = await mysqlServerConnection.query(answersQuery, [
        question.question_id,
      ]);

      // For each answer, fetch the user details (name and email)
      const responses = [];

      for (const answer of answers) {
        const userQuery = `
          SELECT name, email
          FROM ${db_name}.users
          WHERE id = ?
        `;

        // Fetch the user details based on user_id
        const [userDetails] = await mysqlServerConnection.query(userQuery, [
          answer.user_id,
        ]);

        // Push the response with user details
        responses.push({
          answer: answer.answer,
          name: userDetails[0]?.name,
          email: userDetails[0]?.email,
        });
      }

      // Push the question with its responses
      surveyDetails.push({
        question: question.question,
        responses: responses,
      });
    }

    return res.status(200).json({
      success: true,
      data: surveyDetails,
    });
  } catch (error) {
    console.error("Error fetching single survey details:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const singleCourseDetails = async (req, res, next) => {
  try {
    const { course_id } = req.params;
    const [result] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.courses WHERE id = ? AND is_approved = 0 AND is_deleted = 0`,
      [course_id]
    );

    if (result.length === 0) {
      return next({
        statusCode: 404,
        message: "Course not found or is already approved!!",
      });
    }

    res.status(200).json({
      success: true,
      data: result[0],
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getCourseCategories = async (req, res, next) => {
  try {
    const [result] = await mysqlServerConnection.query(
      `SELECT DISTINCT course_category FROM ${req.user.db_name}.courses WHERE is_deleted = 0`
    );

    if (result.length === 0) {
      return next({ statusCode: 404, message: "No course categories foud" });
    }

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getCourseDetails = async (req, res, next) => {
  console.log("📡 === getCourseDetails API Called ===");

  try {
    const { course_id } = req.body;
    const dbname = req.user.db_name;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    // 1. Get course data
    const [courseResult] = await mysqlServerConnection.query(
      `SELECT 
          id,
          course_name,
          course_desc,
          course_price,
          banner_image,
          course_type,
          total_rating,
          created_by,
          course_category,
          course_subcategory,
          levels,
          course_info,
          tags,
          course_language,
          currency
       FROM ${dbname}.courses 
       WHERE id = ? 
         AND is_deleted = 0 
         AND is_active = 1 
         AND is_approved = 1`,
      [course_id]
    );

    if (!courseResult || courseResult.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "Course not found or inactive" });
    }

    const course = courseResult[0];

    // 1.5. Check if course is already purchased by this user
    const [purchaseResult] = await mysqlServerConnection.query(
      `SELECT 1 FROM ${dbname}.mycourses 
       WHERE course_id = ? AND user_id = ? LIMIT 1`,
      [course_id, req.user.userId]
    );
    const is_course_purchase = purchaseResult.length > 0;

    // 2. Trainer details
    const [userResult] = await mysqlServerConnection.query(
      `SELECT name, bio, profile_pic_url 
         FROM ${dbname}.users 
         WHERE id = ? 
           AND is_deleted = 0`,
      [course.created_by]
    );
    const trainer = userResult[0] || {};

    // 3. Role
    let roleName = null;
    const [userRoleResult] = await mysqlServerConnection.query(
      `SELECT role_id 
         FROM ${dbname}.user_roles 
         WHERE user_id = ?`,
      [course.created_by]
    );
    if (userRoleResult.length > 0) {
      const roleId = userRoleResult[0].role_id;
      const [roleResult] = await mysqlServerConnection.query(
        `SELECT name 
           FROM ${dbname}.roles 
           WHERE id = ? 
             AND is_deleted = 0 
             AND is_active = 1`,
        [roleId]
      );
      roleName = roleResult.length > 0 ? roleResult[0].name : null;
    }

    // 4. Module count
    const [moduleCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS totalModules 
         FROM ${dbname}.modules 
         WHERE course_id = ? 
           AND is_deleted = 0 
           AND is_active = 1`,
      [course_id]
    );
    const totalModules = moduleCountResult[0].totalModules || 0;

    // 5. Enrollment count
    const [enrollCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS enrolledCount 
         FROM ${dbname}.mycourses 
         WHERE course_id = ?`,
      [course_id]
    );
    const enrolledCount = enrollCountResult[0].enrolledCount || 0;

    // 6. Total video duration
    function convertToSeconds(timeStr) {
      const parts = timeStr.split(":").map(Number);
      if (parts.length === 3) return parts[0] * 3600 + parts[1] * 60 + parts[2];
      if (parts.length === 2) return parts[0] * 60 + parts[1];
      if (parts.length === 1) return parts[0];
      return 0;
    }

    function formatSecondsToHHMMSS(totalSeconds) {
      const hrs = Math.floor(totalSeconds / 3600);
      const mins = Math.floor((totalSeconds % 3600) / 60);
      const secs = totalSeconds % 60;
      return `${hrs.toString().padStart(2, "0")}:${mins
        .toString()
        .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }

    const [videoDurations] = await mysqlServerConnection.query(
      `SELECT video_duration 
         FROM ${dbname}.videos 
         WHERE course_id = ? 
           AND is_deleted = 0 
           AND is_active = 1`,
      [course_id]
    );
    let totalSeconds = 0;
    videoDurations.forEach((video) => {
      totalSeconds += convertToSeconds(video.video_duration || "0");
    });
    const totalVideoDuration = formatSecondsToHHMMSS(totalSeconds);

    // 7. Modules and contents
    const [modulesResult] = await mysqlServerConnection.query(
      `SELECT id, module_name, module_sequence 
         FROM ${dbname}.modules 
         WHERE course_id = ? 
           AND is_deleted = 0 
           AND is_active = 1 
         ORDER BY module_sequence ASC`,
      [course_id]
    );

    const modules = {};
    for (const module of modulesResult) {
      const moduleId = module.id;
      const moduleName = module.module_name;
      modules[moduleName] = [];

      // Videos
      const [videos] = await mysqlServerConnection.query(
        `SELECT video_name, video_duration 
           FROM ${dbname}.videos 
           WHERE module_id = ? 
             AND is_deleted = 0 
             AND is_active = 1`,
        [moduleId]
      );
      videos.forEach((video) => {
        modules[moduleName].push({
          type: "Video",
          title: video.video_name,
          duration: video.video_duration,
        });
      });

      // Documents
      const [docs] = await mysqlServerConnection.query(
        `SELECT doc_name 
           FROM ${dbname}.documents 
           WHERE module_id = ? 
             AND is_deleted = 0 
             AND is_active = 1`,
        [moduleId]
      );
      docs.forEach((doc) => {
        modules[moduleName].push({
          type: "Document",
          title: doc.doc_name,
          fileType: "PDF",
        });
      });

      // Assessments & Surveys from assessments table
      const [assessments] = await mysqlServerConnection.query(
        `SELECT assessment_name, assessment_type 
           FROM ${dbname}.assessments 
           WHERE module_id = ? 
             AND is_deleted = 0 
             AND is_active = 1 
             AND is_approved = 1`,
        [moduleId]
      );
      assessments.forEach((assessment) => {
        const itemType = assessment.assessment_type?.toLowerCase();
        if (itemType === 'survey' || itemType === 'assessment') {
          modules[moduleName].push({
            type: itemType.charAt(0).toUpperCase() + itemType.slice(1),
            title: assessment.assessment_name || (itemType === 'survey' ? 'Survey' : 'Assessment'),
          });
        }
      });
    }

    // 8. Final response
    return res.status(200).json({
      success: true,
      message: "Course data fetched successfully",
      data: {
        course_id: course.id,
        course_name: course.course_name,
        course_desc: course.course_desc,
        course_price: course.course_price,
        banner_image: course.banner_image,
        course_type: course.course_type,
        course_category: course.course_category,
        course_subcategory: course.course_subcategory,
        levels: course.levels,
        course_info: course.course_info || "",
        tags: course.tags || "",
        course_language: course.course_language || "English",
        currency: course.currency || "USD",
        is_course_purchase: is_course_purchase,
        trainer: {
          name: trainer.name || null,
          role: roleName,
          bio: trainer.bio || null,
          profile: trainer.profile_pic_url || null,
        },
        courseMeta: {
          modulesCount: totalModules,
          enrolledCount: enrolledCount,
          averageRating: course.total_rating || "0",
          totalVideoDuration: totalVideoDuration,
        },
        modules,
      },
    });
  } catch (error) {
    console.error("❌ Error in getCourseDetails:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};





const createCourse = async (req, res) => {
  console.log("course tri----------------------------------ggered");
  console.log("Request body:", req.body);
  console.log("Request file:", req.file);
  console.log('liveDomain', req.body.liveDomain);
  try {
    const file = req.file;
    const result = req.body;
    const created_by = req.user.userId;

    // Map frontend field names to backend field names
    const courseData = {
      course_name: result.courseTitle,
      course_desc: result.courseDescription,
      course_price: result.price,
      course_type: result.courseType || "free",
      course_category: result.courseCategory,
      levels: result.courseLevel,
      course_language: result.courseLanguage,
      currency: result.currency || "USD",
      discount_price: result.discountPrice || null,
      tags: result.tags
        ? JSON.stringify(JSON.parse(result.tags))
        : JSON.stringify([]),
      course_info: result.courseInfo
        ? JSON.stringify(JSON.parse(result.courseInfo))
        : JSON.stringify([]),
      certificate_template_id: Number(result.certificateType) || null,
    };

    console.log("Mapped course data:", courseData);

    // Upload banner image to S3
    let banner = "";
    if (file) {
      console.log("Uploading file to S3...");
      const s3Response = await uploadImageToS3(file.buffer);
      banner = s3Response.path;
      console.log("S3 upload response:", banner);
    }

    // Validation for required fields
    if (
      !courseData.course_name ||
      !courseData.course_desc ||
      !courseData.course_category
    ) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: "Following fields are required",
          response: {
            courseTitle: "Course title is required",
            courseDescription: "Course description is required",
            courseCategory: "Course category is required",
            courseLevel: "Course level is required",
            courseLanguage: "Course language is required",
            certificateType: "Certificate type is required",
          },
        },
      });
    }

    console.log("Inserting into database...");
    const insertResult = await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.courses (
            created_by,
            banner_image,
            course_name,
            course_desc,
            course_price,
            course_type,
            course_category,
            levels,
            course_language,
            currency,
            discount_price,
            tags,
            course_info,
            certificate_template_id
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        created_by,
        banner,
        courseData.course_name,
        courseData.course_desc,
        courseData.course_price,
        courseData.course_type,
        courseData.course_category,
        courseData.levels,
        courseData.course_language,
        courseData.currency,
        courseData.discount_price,
        courseData.tags,
        courseData.course_info,
        courseData.certificate_template_id,
      ]
    );

    console.log("Database insert result:", insertResult);

    // Generate public course URL after successful insertion
    const courseId = insertResult[0].insertId;
    const liveDomain = result.liveDomain;

    // Extract domain from the full URL if needed
    let domain = liveDomain;
    if (liveDomain && liveDomain.includes('://')) {
      try {
        const url = new URL(liveDomain);
        domain = url.hostname + (url.port ? `:${url.port}` : '');
      } catch (error) {
        console.error('Error parsing domain URL:', error);
        domain = 'localhost';
      }
    }

    // Fallback to localhost if domain is undefined or empty
    if (!domain || domain === 'undefined') {
      domain = 'localhost';
    }

    console.log('Using domain for course URL:', domain);

    // Create the course data object to encode
    const courseDataToEncode = { id: courseId };
    const encodedCourseData = encodeData(courseDataToEncode);

    const FRONTEND_URL = process.env.FRONTEND_URL;

    // Generate the public course URL
    const publicCourseUrl = `${FRONTEND_URL}/public/courseDetails/${encodedCourseData}`;

    console.log("Generated public course URL:", publicCourseUrl);

    // Update the course with the generated URL
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.courses 
       SET course_url = ? 
       WHERE id = ?`,
      [publicCourseUrl, courseId]
    );

    console.log("Course URL updated in database");

    // notification trigger

    res.status(201).json({
      success: true,
      data: {
        message: "Course created successfully.",
        response: {
          ...courseData,
          id: insertResult[0].insertId,
          banner_image: banner,
        },
      },
    });
  } catch (error) {
    console.error("Error creating course:", error);
    console.error("Error stack:", error.stack);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error: " + error.message },
    });
  }
};

const getCourseApprovalANDApprovedANDRejected = async (req, res, next) => {
  try {
    const db_name = req.user.db_name;

    console.log("Fetching approval data from:", db_name);

    // 1. Pending approval requests
    const [approve] = await mysqlServerConnection.query(`
        SELECT
          c.id,
          c.course_name,
          c.course_desc,
          c.banner_image,
          c.created_by AS trainer_id,
          u.name AS trainer_name,
          u.email AS trainer_email,
          'pending' AS status,
          NULL AS rejected_note,
          c.is_approved,
          c.createdAt AS request_date
        FROM ${db_name}.courses c
        JOIN ${db_name}.users u ON c.created_by = u.id
        WHERE c.send_request = 1 AND c.is_approved = 0 AND c.is_rejected = 0 AND c.is_deleted = 0
      `);

    // 2. Approved courses
    const [approved] = await mysqlServerConnection.query(`
        SELECT
          c.id,
          c.course_name,
          c.course_desc,
          c.banner_image,
          c.created_by AS trainer_id,
          u.name AS trainer_name,
          u.email AS trainer_email,
          'approved' AS status,
          NULL AS rejected_note,
          c.is_approved,
          c.createdAt AS request_date
        FROM ${db_name}.courses c
        JOIN ${db_name}.users u ON c.created_by = u.id
        WHERE c.is_approved = 1 AND c.is_deleted = 0
      `);

    // 3. Rejected courses
    const [rejected] = await mysqlServerConnection.query(`
        SELECT
          c.id,
          c.course_name,
          c.course_desc,
          c.banner_image,
          c.created_by AS trainer_id,
          u.name AS trainer_name,
          u.email AS trainer_email,
          'rejected' AS status,
          c.rejected_note,
          c.is_approved,
          c.createdAt AS request_date
        FROM ${db_name}.courses c
        JOIN ${db_name}.users u ON c.created_by = u.id
        WHERE c.is_approved = 0 AND c.is_rejected = 1 AND c.is_deleted = 0
      `);

    // Fetch content summary (videos, docs, assessments, etc.) for all courses
    const summarySQL = `
      SELECT
        c.id AS course_id,
        IFNULL(v.video_count, 0) AS total_videos,
        IFNULL(doc.doc_count, 0) AS total_documents,
        IFNULL(a.assessment_count, 0) AS total_assessments,
        IFNULL(s.survey_count, 0) AS total_surveys,
        SEC_TO_TIME(IFNULL(v.total_duration, 0)) AS total_duration,
        IFNULL(mc.purchase_count, 0) AS total_purchased,
        ROUND(IFNULL(cr.avg_rating, 0), 1) AS average_rating
      FROM ${db_name}.courses c
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS video_count, SUM(TIME_TO_SEC(video_duration)) AS total_duration
        FROM ${db_name}.videos
        WHERE is_deleted = 0 AND is_active = 1
        GROUP BY course_id
      ) v ON v.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS doc_count
        FROM ${db_name}.documents
        WHERE is_deleted = 0 AND is_active = 1
        GROUP BY course_id
      ) doc ON doc.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS assessment_count
        FROM ${db_name}.assessments
        WHERE is_deleted = 0 AND is_active = 1 AND assessment_type = 'assessment'
        GROUP BY course_id
      ) a ON a.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS survey_count
        FROM ${db_name}.assessments
        WHERE is_deleted = 0 AND is_active = 1 AND assessment_type = 'survey'
        GROUP BY course_id
      ) s ON s.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS purchase_count
        FROM ${db_name}.mycourses
        GROUP BY course_id
      ) mc ON mc.course_id = c.id
      LEFT JOIN (
        SELECT course_id, AVG((class_rating + mentor_rating)/2) AS avg_rating
        FROM ${db_name}.course_reviews
        GROUP BY course_id
      ) cr ON cr.course_id = c.id
      WHERE c.is_deleted = 0
    `;

    const [summary] = await mysqlServerConnection.query(summarySQL);

    // Map summary to course id
    const summaryMap = {};
    summary.forEach((s) => {
      summaryMap[s.course_id] = {
        total_videos: s.total_videos,
        total_documents: s.total_documents,
        total_assessments: s.total_assessments,
        total_surveys: s.total_surveys,
        total_duration: s.total_duration,
        total_purchased: s.total_purchased,
        average_rating: s.average_rating,
      };
    });

    // Helper function to attach course summary to each course
    const attachCourseSummary = (courses) => {
      return courses.map((course) => ({
        ...course,
        course_summary: summaryMap[course.id] || {
          total_videos: 0,
          total_documents: 0,
          total_assessments: 0,
          total_surveys: 0,
          total_duration: "00:00:00",
          total_purchased: 0,
          average_rating: 0,
        },
      }));
    };

    // Attach course summary to all course arrays
    const approveWithSummary = attachCourseSummary(approve);
    const approvedWithSummary = attachCourseSummary(approved);
    const rejectedWithSummary = attachCourseSummary(rejected);

    return res.status(200).json({
      success: true,
      message: "Fetched course approval data",
      data: {
        approve: approveWithSummary,
        approved: approvedWithSummary,
        rejected: rejectedWithSummary,
      },
    });
  } catch (error) {
    console.error("Error in getCourseApprovalANDApprovedANDRejected:", error);
    return next({
      statusCode: 500,
      message: "Internal server error",
    });
  }
};

const courseApproving = async (req, res, next) => {
  try {
    const { course_id } = req.body;
    const db_name = req.user.db_name; // e.g. "admin_lms"

    console.log("🟢 Incoming course approval request");
    console.log("➡️ course_id:", course_id);
    console.log("➡️ db_name:", db_name);

    // 1. Verify course exists and is not deleted
    console.log("🔍 Checking if course exists…");
    const [courseRows] = await mysqlServerConnection.query(
      `SELECT 
           id,
           course_name,
           is_approved,
           send_request,
           is_rejected,
           is_deleted
         FROM ${db_name}.courses
         WHERE id = ? AND is_deleted = 0`,
      [course_id]
    );

    console.log("📦 Course lookup result:", courseRows);
    if (!courseRows || courseRows.length === 0) {
      console.log("❌ Course not found or has been deleted");
      return res.status(404).json({
        success: false,
        message: "Course not found or has been deleted",
      });
    }

    const course = courseRows[0];
    if (course.is_approved) {
      return res.status(200).json({
        success: true,
        message: "Course is already approved",
      });
    }

    // 2. Approve the course: set is_approved=1, is_rejected=0, send_request=0
    console.log("✅ Approving course in DB…");
    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${db_name}.courses
           SET 
             is_approved   = 1,
             is_rejected   = 0,
             send_request  = 0,
             updatedAt     = NOW()
         WHERE id = ?`,
      [course_id]
    );

    console.log("📌 Update result:", updateResult);
    if (updateResult.affectedRows === 0) {
      throw new Error("No rows were updated – course may not exist");
    }

    return res.status(200).json({
      success: true,
      message: "Course approved successfully",
    });
  } catch (error) {
    console.error("🚨 Error in courseApproving:", error);

    if (error.code === "ER_BAD_FIELD_ERROR") {
      console.error("Database field error – possible schema mismatch");
      return res.status(500).json({
        success: false,
        message: "Database configuration error. Please check the schema.",
        error: error.message,
      });
    }

    return next({
      statusCode: 500,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const courseReject = async (req, res, next) => {
  try {
    const { course_id, rejected_note } = req.body;
    const db_name = req.user.db_name; // e.g. "admin_lms"

    console.log("🟢 Incoming course rejection request");
    console.log("➡️ course_id:", course_id);
    console.log("➡️ rejected_note:", rejected_note);
    console.log("➡️ db_name:", db_name);

    // 1. Verify course exists and is not deleted
    console.log("🔍 Checking if course exists…");
    const [courseRows] = await mysqlServerConnection.query(
      `SELECT 
           id,
           course_name,
           is_approved,
           send_request,
           is_rejected,
           is_deleted
         FROM ${db_name}.courses
         WHERE id = ? AND is_deleted = 0`,
      [course_id]
    );

    console.log("📦 Course lookup result:", courseRows);
    if (!courseRows || courseRows.length === 0) {
      console.log("❌ Course not found or has been deleted");
      return res.status(404).json({
        success: false,
        message: "Course not found or has been deleted",
      });
    }

    // 2. Update the course to mark it as rejected
    console.log("✅ Rejecting course in DB…");
    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${db_name}.courses
           SET 
             is_rejected    = 1,
             rejected_note  = ?,
             is_approved    = 0,
             send_request   = 0,
             updatedAt      = NOW()
         WHERE id = ?`,
      [rejected_note, course_id]
    );

    console.log("📌 Update result:", updateResult);
    if (updateResult.affectedRows === 0) {
      throw new Error("No rows were updated – course may not exist");
    }

    return res.status(200).json({
      success: true,
      message: "Course rejected successfully",
    });
  } catch (error) {
    console.error("🚨 Error in courseReject:", error);

    if (error.code === "ER_BAD_FIELD_ERROR") {
      console.error("Database field error – possible schema mismatch");
      return res.status(500).json({
        success: false,
        message: "Database configuration error. Please check the schema.",
        error: error.message,
      });
    }

    return next({
      statusCode: 500,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const getCourseDetailsByIdForNotification = async (req, res, next) => {
  try {
    const { course_id } = req.body;
    const db_name = req.user.db_name; // e.g. "admin_lms"

    console.log("🟢 Fetching course details for notification");
    console.log("➡️ course_id:", course_id);
    console.log("➡️ db_name:", db_name);

    // ✅ SQL filters out invalid courses directly
    const [courseCheck] = await mysqlServerConnection.query(
      `SELECT 
           c.id AS course_id,
           c.course_name,
           c.banner_image,
           c.course_desc,
           u.name AS trainer_name
         FROM ${db_name}.courses c
         LEFT JOIN ${db_name}.users u ON c.created_by = u.id
         WHERE 
           c.id = ?
           AND c.is_active = 1
           AND c.is_approved = 1
           AND c.is_deleted = 0
           AND c.is_rejected = 0`,
      [course_id]
    );

    if (!courseCheck || courseCheck.length === 0) {
      console.log(
        "❌ Course is invalid (not found, not approved, rejected, deleted, or inactive)"
      );
      return res.status(404).json({
        success: false,
        message: "Course not found or deleted",
      });
    }

    const course = courseCheck[0];

    return res.status(200).json({
      success: true,
      data: {
        course_id: course.course_id,
        course_name: course.course_name,
        imageName: course.banner_image,
        description: course.course_desc,
        trainer_name: course.trainer_name || "Unknown",
      },
    });
  } catch (error) {
    console.error("🚨 Error in getCourseDetailsByIdForNotification:", error);

    if (error.code === "ER_BAD_FIELD_ERROR") {
      return res.status(500).json({
        success: false,
        message: "Database configuration error. Please check the schema.",
        error: error.message,
      });
    }

    return next({
      statusCode: 500,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const getCourseDetailsByID = async (req, res, next) => {
  try {
    const { course_id } = req.body;

    // Validate course_id
    if (!course_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Course ID is required",
        },
      });
    }

    console.log("Fetching course details for ID:", course_id);

    // Fetch course details from database
    const [courseResults] = await mysqlServerConnection.query(
      `SELECT
        id,
        created_by,
        banner_image,
        course_name,
        course_desc,
        course_price,
        course_type,
        course_category,
        levels,
        course_language,
        currency,
        discount_price,
        tags,
        course_info,
        certificate_template_id,
        total_rating,
        subscribers,
        is_active,
        is_approved,
        is_rejected,
        send_request,
        rejected_note,
        createdAt,
        updatedAt
      FROM ${req.user.db_name}.courses
      WHERE id = ? AND is_deleted = 0`,
      [course_id]
    );

    // Check if course exists
    if (courseResults.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "Course not found",
        },
      });
    }

    const course = courseResults[0];

    console.log("Raw course data from DB:");
    console.log("Tags:", course.tags, "Type:", typeof course.tags);
    console.log(
      "Course Info:",
      course.course_info,
      "Type:",
      typeof course.course_info
    );

    // Handle JSON fields (MySQL driver auto-parses JSON columns)
    let parsedTags = [];
    let parsedCourseInfo = [];

    // Handle tags - check if it's already an array or needs parsing
    if (course.tags) {
      if (Array.isArray(course.tags)) {
        parsedTags = course.tags;
        console.log("Tags already parsed as array:", parsedTags);
      } else if (typeof course.tags === "string") {
        try {
          parsedTags = JSON.parse(course.tags);
          console.log("Parsed tags from string:", parsedTags);
        } catch (error) {
          console.warn(
            "Error parsing tags JSON:",
            error,
            "Raw tags:",
            course.tags
          );
          parsedTags = [];
        }
      }
    }

    // Handle course_info - check if it's already an array or needs parsing
    if (course.course_info) {
      if (Array.isArray(course.course_info)) {
        parsedCourseInfo = course.course_info;
        console.log("Course info already parsed as array:", parsedCourseInfo);
      } else if (typeof course.course_info === "string") {
        try {
          parsedCourseInfo = JSON.parse(course.course_info);
          console.log("Parsed course_info from string:", parsedCourseInfo);
        } catch (error) {
          console.warn(
            "Error parsing course_info JSON:",
            error,
            "Raw course_info:",
            course.course_info
          );
          parsedCourseInfo = [];
        }
      }
    }

    // Format the response
    const courseDetails = {
      id: course.id,
      created_by: course.created_by,
      banner_image: course.banner_image,
      course_name: course.course_name,
      course_desc: course.course_desc,
      course_price: course.course_price,
      course_type: course.course_type,
      course_category: course.course_category,
      levels: course.levels,
      course_language: course.course_language,
      currency: course.currency,
      discount_price: course.discount_price,
      tags: parsedTags,
      course_info: parsedCourseInfo,
      certificate_template_id: course.certificate_template_id,
      total_rating: course.total_rating,
      subscribers: course.subscribers,
      is_active: course.is_active,
      is_approved: course.is_approved,
      is_rejected: course.is_rejected,
      send_request: course.send_request,
      rejected_note: course.rejected_note,
      created_at: course.createdAt,
      updated_at: course.updatedAt,
    };

    console.log(
      "Course details fetched successfully:",
      courseDetails.course_name
    );

    res.status(200).json({
      success: true,
      data: {
        message: "Course details fetched successfully",
        course: courseDetails,
      },
    });
  } catch (error) {
    console.error("Error fetching course details:", error.message);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal Server Error: " + error.message,
      },
    });
  }
};

const getPublicCourseDetails = async (req, res, next) => {
  const { course_id, domain } = req.body;

  console.log("🔍 Request Body:", req.body);

  try {
    if (!course_id || !domain) {
      console.log("⚠️ Missing course_id or domain");
      return res.status(400).json({
        success: false,
        message: "Course ID and domain are required",
      });
    }

    // Step 1: Get organization database name
    console.log("🔎 Fetching DB name for domain:", domain);
    const [orgResult] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${mainDbName}.organization WHERE auth_sub_domain = ?`,
      [domain]
    );

    if (orgResult.length === 0) {
      console.log("❌ Organization not found for domain:", domain);
      return res.status(404).json({
        success: false,
        message: "Organization not found",
      });
    }

    const dbname = orgResult[0].db_name;
    console.log("✅ DB Name:", dbname);

    // Step 2: Get course data
    console.log("📘 Fetching course details for ID:", course_id);
    const [courseResult] = await mysqlServerConnection.query(
      `SELECT 
        id, course_name, course_desc, course_price, banner_image,
        course_type, total_rating, created_by, course_category,
        course_subcategory, levels, course_info, tags, course_language, currency
       FROM ${dbname}.courses 
       WHERE id = ? AND is_deleted = 0 AND is_active = 1 AND is_approved = 1`,
      [course_id]
    );

    if (!courseResult || courseResult.length === 0) {
      console.log("❌ Course not found or not approved:", course_id);
      return res.status(404).json({
        success: false,
        message: "Course not found or inactive",
      });
    }

    const course = courseResult[0];
    console.log("✅ Course Found:", course.course_name);

    // Step 3: Get trainer details
    console.log("👤 Fetching trainer details for user ID:", course.created_by);
    const [userResult] = await mysqlServerConnection.query(
      `SELECT name, bio, profile_pic_url 
       FROM ${dbname}.users 
       WHERE id = ? AND is_deleted = 0`,
      [course.created_by]
    );
    const trainer = userResult[0] || {};
    console.log("✅ Trainer Info:", trainer.name);

    // Step 4: Get trainer role
    let roleName = null;
    console.log("🔎 Fetching role for user ID:", course.created_by);
    const [userRoleResult] = await mysqlServerConnection.query(
      `SELECT role_id FROM ${dbname}.user_roles WHERE user_id = ?`,
      [course.created_by]
    );
    if (userRoleResult.length > 0) {
      const roleId = userRoleResult[0].role_id;
      const [roleResult] = await mysqlServerConnection.query(
        `SELECT name FROM ${dbname}.roles WHERE id = ? AND is_deleted = 0 AND is_active = 1`,
        [roleId]
      );
      roleName = roleResult.length > 0 ? roleResult[0].name : null;
      console.log("✅ Trainer Role:", roleName);
    }

    // Step 5: Get module count
    console.log("📦 Counting total modules...");
    const [moduleCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS totalModules 
       FROM ${dbname}.modules 
       WHERE course_id = ? AND is_deleted = 0 AND is_active = 1`,
      [course_id]
    );
    const totalModules = moduleCountResult[0].totalModules || 0;
    console.log("✅ Total Modules:", totalModules);

    // Step 6: Get enrollment count
    console.log("👥 Counting enrolled students...");
    const [enrollCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS enrolledCount FROM ${dbname}.mycourses WHERE course_id = ?`,
      [course_id]
    );
    const enrolledCount = enrollCountResult[0].enrolledCount || 0;
    console.log("✅ Enrolled Count:", enrolledCount);

    // Step 7: Calculate total video duration
    console.log("⏱ Fetching video durations...");
    const [videoDurations] = await mysqlServerConnection.query(
      `SELECT video_duration FROM ${dbname}.videos 
       WHERE course_id = ? AND is_deleted = 0 AND is_active = 1`,
      [course_id]
    );

    function convertToSeconds(timeStr) {
      const parts = timeStr.split(":").map(Number);
      if (parts.length === 3) return parts[0] * 3600 + parts[1] * 60 + parts[2];
      if (parts.length === 2) return parts[0] * 60 + parts[1];
      if (parts.length === 1) return parts[0];
      return 0;
    }

    function formatSecondsToHHMMSS(totalSeconds) {
      const hrs = Math.floor(totalSeconds / 3600);
      const mins = Math.floor((totalSeconds % 3600) / 60);
      const secs = totalSeconds % 60;
      return `${hrs.toString().padStart(2, "0")}:${mins
        .toString()
        .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }

    let totalSeconds = 0;
    videoDurations.forEach((video) => {
      totalSeconds += convertToSeconds(video.video_duration || "0");
    });
    const totalVideoDuration = formatSecondsToHHMMSS(totalSeconds);
    console.log("✅ Total Video Duration:", totalVideoDuration);

    // Step 8: Fetch module-wise content
    console.log("📚 Fetching modules and contents...");
    const [modulesResult] = await mysqlServerConnection.query(
      `SELECT id, module_name, module_sequence 
       FROM ${dbname}.modules 
       WHERE course_id = ? AND is_deleted = 0 AND is_active = 1 
       ORDER BY module_sequence ASC`,
      [course_id]
    );

    const modules = {};
    for (const module of modulesResult) {
      const moduleId = module.id;
      const moduleName = module.module_name;
      modules[moduleName] = [];

      console.log(`🧩 Module: ${moduleName} (ID: ${moduleId})`);

      // Videos
      const [videos] = await mysqlServerConnection.query(
        `SELECT video_name, video_duration 
         FROM ${dbname}.videos 
         WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
        [moduleId]
      );
      videos.forEach((video) => {
        modules[moduleName].push({
          type: "Video",
          title: video.video_name,
          duration: video.video_duration,
        });
      });

      // Documents
      const [docs] = await mysqlServerConnection.query(
        `SELECT doc_name 
         FROM ${dbname}.documents 
         WHERE module_id = ? AND is_deleted = 0 AND is_active = 1`,
        [moduleId]
      );
      docs.forEach((doc) => {
        modules[moduleName].push({
          type: "Document",
          title: doc.doc_name,
          fileType: "PDF",
        });
      });

      // Assessments
      const [assessments] = await mysqlServerConnection.query(
        `SELECT assessment_name, assessment_type 
         FROM ${dbname}.assessments 
         WHERE module_id = ? AND is_deleted = 0 AND is_active = 1 AND is_approved = 1`,
        [moduleId]
      );
      assessments.forEach((assessment) => {
        const itemType = assessment.assessment_type?.toLowerCase();
        if (itemType === 'survey' || itemType === 'assessment') {
          modules[moduleName].push({
            type: itemType.charAt(0).toUpperCase() + itemType.slice(1),
            title: assessment.assessment_name || (itemType === 'survey' ? 'Survey' : 'Assessment'),
          });
        }
      });

      console.log(`✅ ${moduleName} contents loaded:`, modules[moduleName].length);
    }

    // Step 9: Final Response
    console.log("✅ All course data fetched successfully");

    return res.status(200).json({
      success: true,
      message: "Course data fetched successfully",
      data: {
        course_id: course.id,
        course_name: course.course_name,
        course_desc: course.course_desc,
        course_price: course.course_price,
        banner_image: course.banner_image,
        course_type: course.course_type,
        course_category: course.course_category,
        course_subcategory: course.course_subcategory,
        levels: course.levels,
        course_info: course.course_info || "",
        tags: course.tags || "",
        course_language: course.course_language || "English",
        currency: course.currency || "USD",
        trainer: {
          name: trainer.name || null,
          role: roleName,
          bio: trainer.bio || null,
          profile: trainer.profile_pic_url || null,
        },
        courseMeta: {
          modulesCount: totalModules,
          enrolledCount: enrolledCount,
          averageRating: course.total_rating || "0",
          totalVideoDuration: totalVideoDuration,
        },
        modules,
      },
    });
  } catch (error) {
    console.error("❌ Error in getCourseDetails:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};




const getAssessmentAnalyticsUsingGemini = async (req, res) => {
  try {
    const dbName = req.user.db_name;
    const { assessment_id } = req.body;

    console.log("🔵 Incoming Request Payload:", req.body);
    console.log("📂 Using Database:", dbName);

    if (!assessment_id) {
      console.warn("⚠️ assessment_id not provided");
      return res.status(400).json({ success: false, message: "assessment_id is required" });
    }

    // Step 1: Fetch assessment
    const [assessmentRows] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.assessments WHERE id = ?`,
      [assessment_id]
    );
    console.log("📘 Assessment Query Result:", assessmentRows);

    const assessment = assessmentRows[0];
    if (!assessment) throw new Error("Assessment not found");

    // Step 2: Fetch questions
    const [questions] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.questions WHERE assessment_id = ?`,
      [assessment_id]
    );
    console.log("📘 Questions Fetched:", questions.length);

    // Step 3: Fetch answers
    const [answers] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.question_answer WHERE assessment_id = ?`,
      [assessment_id]
    );
    console.log("📘 Answers Fetched:", answers.length);

    // Step 3.5: Fetch question options to map answer IDs to actual text
    const [questionOptions] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.question_options WHERE question_id IN (SELECT id FROM ${dbName}.questions WHERE assessment_id = ?)`,
      [assessment_id]
    );
    console.log("📘 Question Options Fetched:", questionOptions.length);

    // Create a mapping of option ID to content value
    const optionMap = {};
    questionOptions.forEach(option => {
      optionMap[option.id] = {
        content: option.content_value,
        option_key: option.option_key,
        is_correct: option.is_correct
      };
    });

    // Enhance answers with actual answer text
    const enhancedAnswers = answers.map(answer => {
      let actualAnswerText = 'Unknown';
      let selectedOptions = [];
      
      try {
        // Parse the answer JSON array
        const answerIds = JSON.parse(answer.answer);
        if (Array.isArray(answerIds)) {
          selectedOptions = answerIds.map(id => {
            const option = optionMap[id];
            return option ? option.content : `Option ID: ${id}`;
          });
          actualAnswerText = selectedOptions.join(', ');
        }
      } catch (e) {
        actualAnswerText = answer.answer || 'Invalid answer format';
      }
      
      return {
        ...answer,
        actual_answer_text: actualAnswerText,
        selected_options: selectedOptions
      };
    });

    console.log("📘 Enhanced Answers Sample:", enhancedAnswers.slice(0, 3));

    // Step 4: Compute stats
    const questionStats = {};
    questions.forEach(q => {
      questionStats[q.id] = { total: 0, correct: 0, wrong: 0 };
    });
    enhancedAnswers.forEach(a => {
      const stats = questionStats[a.question_id];
      if (stats) {
        stats.total += 1;
        if (a.is_correct === 1) {
          stats.correct += 1;
        } else {
          stats.wrong += 1;
        }
      }
    });
    console.log("📊 Computed Question Stats:", questionStats);

    // Step 5: Build prompt
    const prompt = `
You are an AI assessment analyst. Analyze the following data and provide insights:

1. ✅ Highlight well-understood questions
2. ❌ Identify questions with low correctness or confusion
3. 📉 Suggest improvements (rewording, clarity, etc.)
4. 🧠 Assign difficulty level to each question
5. 📊 Provide overall assessment summary

Assessment:
- Title: ${assessment.assessment_name}
- Duration: ${assessment.duration} mins
- Pass Percentage: ${assessment.pass_percentage}%

Questions:
${questions.map(q => {
      const stats = questionStats[q.id];
      return `
Q${q.id}: ${q.question}
Type: ${q.question_type}
Options: ${q.options}
Correct Answer: ${q.correct_option}
Stats: Total: ${stats.total}, Correct: ${stats.correct}, Wrong: ${stats.wrong}
`.trim();
    }).join("\n")}

User Answers:
${enhancedAnswers.map(a => `
User ${a.user_id} | Q${a.question_id} | Answer Text: "${a.actual_answer_text}" | Correct: ${a.is_correct}
`).join("\n")}
`;

    console.log("📤 Prompt being sent to Gemini:\n", prompt.slice(0, 1000) + '...'); // print first 1000 chars only

    // Step 6: Call Gemini API
    console.log("🔑 Gemini API Key available:", !!process.env.GEMINI_API_KEY);
    console.log("🔑 Gemini API Key length:", process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.length : 0);
    console.log("🔑 Gemini API Key starts with:", process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.substring(0, 10) + '...' : 'NOT_SET');
    console.log("🔗 Gemini API URL:", `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY ? '***' : 'NOT_SET'}`);
    console.log("📝 Prompt length:", prompt.length);
    console.log("📝 Prompt preview:", prompt.substring(0, 200) + '...');
    console.log("🌐 Environment check - NODE_ENV:", process.env.NODE_ENV);
    console.log("🌐 Environment check - GEMINI_API_KEY exists:", !!process.env.GEMINI_API_KEY);

    let geminiText = "Gemini API analysis not available";

    try {
      console.log("🚀 Making Gemini API request...");
      console.log("📤 Request details:", {
        url: `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY ? '***' : 'NOT_SET'}`,
        method: 'POST',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        },
        dataSize: JSON.stringify({
          contents: [
            {
              role: "user",
              parts: [{ text: `[Prompt length: ${prompt.length} characters]` }]
            }
          ]
        }).length
      });

      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`,
        {
          contents: [
            {
              role: "user",
              parts: [{ text: prompt }]
            }
          ]
        },
        {
          timeout: 30000, // 30 second timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log("✅ Gemini API Raw Response:", response.data);

      geminiText = response.data?.candidates?.[0]?.content?.parts?.[0]?.text || "No response from Gemini";

      console.log("🟢 Final Gemini Analysis Text:", geminiText);

    } catch (geminiError) {
      console.error("❌ Gemini API Error Details:");
      console.error("  - Error Message:", geminiError.message);
      console.error("  - Status Code:", geminiError.response?.status);
      console.error("  - Status Text:", geminiError.response?.statusText);
      console.error("  - Response Data:", geminiError.response?.data);
      console.error("  - Response Headers:", geminiError.response?.headers);
      console.error("  - Request URL:", geminiError.config?.url);
      console.error("  - Request Method:", geminiError.config?.method);
      console.error("  - Request Headers:", geminiError.config?.headers);
      console.error("  - API Key in URL:", geminiError.config?.url?.includes('key=') ? 'Present' : 'Missing');
      console.error("  - Full Error Object:", JSON.stringify(geminiError, null, 2));

      // Provide fallback analysis when Gemini fails
      geminiText = `Assessment Analysis (Fallback - Gemini API Unavailable)

Assessment: ${assessment.assessment_name}
Duration: ${assessment.duration} mins
Pass Percentage: ${assessment.pass_percentage}%

Question Analysis:
${questions.map(q => {
        const stats = questionStats[q.id];
        const correctRate = stats.total > 0 ? ((stats.correct / stats.total) * 100).toFixed(1) : 0;
        return `
Q${q.id}: ${q.question.substring(0, 100)}...
- Correct Rate: ${correctRate}% (${stats.correct}/${stats.total})
- Difficulty: ${correctRate >= 80 ? 'Easy' : correctRate >= 60 ? 'Medium' : 'Hard'}
- Status: ${stats.correct === stats.total ? '✅ Well understood' : stats.correct === 0 ? '❌ Needs attention' : '⚠️ Partially understood'}
`.trim();
      }).join("\n")}

Overall Summary:
- Total Questions: ${questions.length}
- Total Attempts: ${answers.length}
- Questions with 100% success: ${Object.values(questionStats).filter(s => s.correct === s.total && s.total > 0).length}
- Questions with 0% success: ${Object.values(questionStats).filter(s => s.correct === 0 && s.total > 0).length}

Note: This is a fallback analysis as the Gemini AI service is currently unavailable.`;
    }

    return res.status(200).json({
      success: true,
      message: "Assessment analytics generated successfully",
      analysis: geminiText,
      stats: questionStats,
      geminiAvailable: geminiText !== "Gemini API analysis not available"
    });

  } catch (err) {
    console.error("❌ Error during Assessment Analytics:", err.message);
    return res.status(500).json({
      success: false,
      message: err.message,
      error: "Failed to generate assessment analytics"
    });
  }
};

const generateShareUrlForCourse = async (req, res) => {
  try {
    const { courseId, domain } = req.body;
    
    if (!courseId) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Course ID is required" }
      });
    }

    // Use provided domain or fallback to localhost
    let finalDomain = domain || 'test.localhost:3001';
    
    // Extract domain from the full URL if needed
    if (finalDomain && finalDomain.includes('://')) {
      try {
        const url = new URL(finalDomain);
        finalDomain = url.hostname + (url.port ? `:${url.port}` : '');
      } catch (error) {
        console.error('Error parsing domain URL:', error);
        finalDomain = 'test.localhost:3001';
      }
    }

    // Create the course data object to encode
    const courseDataToEncode = { id: parseInt(courseId) };
    const encodedCourseData = encodeData(courseDataToEncode);

    // Generate the public course URL
    const publicCourseUrl = `http://${finalDomain}/public/courseDetails/${encodedCourseData}`;

    console.log("Generated public course URL:", publicCourseUrl);

    res.status(200).json({
      success: true,
      data: {
        courseId: courseId,
        shareUrl: publicCourseUrl,
        encodedData: encodedCourseData,
        domain: finalDomain
      }
    });

  } catch (error) {
    console.error("Error generating share URL:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error: " + error.message }
    });
  }
};

module.exports = {
  createCourse,
  updateCourse,
  showAllCourses,
  getCourseDetails,
  getMyAllCourses,
  createCourseModule,
  getModule,
  getModulesList,
  getModuleContent,
  addVideoAndDetails,
  bulkUploadVideos,
  addToMyCourse,
  getRecentVideos,
  saveRecentVideo,
  getCourseCompletionPercentage,
  getCourseMetadata,
  fetchCoursesByClassroom,
  updateSerialNumber,
  updateContentOrder,
  getCoursePassFailStats,
  getUserAssessmentDetails,
  CourseUserActivity,
  singleCourseDetails,
  getCourseCategories,
  getCourseApprovalANDApprovedANDRejected,
  courseApproving,
  courseReject,
  getCourseDetailsByIdForNotification,
  checkCoursePurchase,
  getSurveysByCourseId,
  getSingleSurveyDetails,
  getAssessmentsByCourseId,
  getAssessmentDetails,
  getCourseDetailsByID,
  getPublicCourseDetails,
  getAssessmentAnalyticsUsingGemini,
  generateShareUrlForCourse,
  getCourseForLandingPage
};
