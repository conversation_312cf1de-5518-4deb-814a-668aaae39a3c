import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';

function Breadcrumbs() {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Filter out empty strings and 'user'
  const pathnames = location.pathname
    .split('/')
    .filter((x) => x !== '' && x !== 'user');

  const handleBack = () => {
    navigate(-1);
  };

  // Create a mapping of path segments to readable names and icons
  const pathMap = {
    'dashboard': {
      name: 'Dashboard',
      icon: 'fluent:home-24-regular'
    },
    'profile': {
      name: 'Profile',
      icon: 'fluent:person-24-regular'
    },
    'courses': {
      name: 'Courses',
      icon: 'fluent:book-24-regular'
    },
    'feed': {
      name: 'Feed',
      icon: 'fluent:document-24-regular'
    },
    'my-feed': {
      name: 'My Feed',
      icon: 'fluent:document-24-regular'
    },
    'courseDetails': {
      name: 'Course Details',
      icon: 'fluent:book-information-24-regular'
    },
    'WatchCourse': {
      name: 'Course Content',
      icon: 'fluent:book-open-24-regular'
    },
    'orderDetails': {
      name: 'Order Details',
      icon: 'fluent:receipt-24-regular'
    },
    'orderDetailsWithPayu': {
      name: 'Order Details',
      icon: 'fluent:receipt-24-regular'
    },
    'successPayment': {
      name: 'Payment Successful',
      icon: 'fluent:checkmark-circle-24-regular'
    },
    'AllClassroom': {
      name: 'Classrooms',
      icon: 'fluent:class-24-regular'
    },
    'certificates': {
      name: 'Certificates',
      icon: 'fluent:certificate-24-regular'
    },
    'notifications': {
      name: 'Notifications',
      icon: 'fluent:alert-24-regular'
    },
    'notificationDetails': {
      name: 'Notification Details',
      icon: 'fluent:alert-urgent-24-regular'
    },
    'settings': {
      name: 'Settings',
      icon: 'fluent:settings-24-regular'
    },
    'help': {
      name: 'Help',
      icon: 'fluent:question-circle-24-regular'
    },
    'faq': {
      name: 'FAQ',
      icon: 'fluent:book-question-mark-24-regular'
    },
    'ticket': {
      name: 'Tickets',
      icon: 'fluent:ticket-24-regular'
    },
    'classroomDetails': {
      name: 'Classroom Dashboard',
      icon: 'fluent:classroom-24-regular'
    },
    'assignmentsQuestions': {
      name: 'Assignment',
      icon: 'fluent:task-list-24-regular'
    }
  };

  const getPathInfo = (path) => {
    // Handle encoded IDs
    if (path.includes('%') || path.includes('eyJ')) {
      const pathType = pathnames[pathnames.indexOf(path) - 1];
      switch (pathType) {
        case 'courses':
        case 'courseDetails':
          return {
            name: 'Course Details',
            icon: 'fluent:book-information-24-regular'
          };
        case 'WatchCourse':
          return {
            name: 'Course Content',
            icon: 'fluent:book-open-24-regular'
          };
        case 'AllClassroom':
        case 'classroomDetails':
          return {
            name: 'Classroom Dashboard',
            icon: 'fluent:classroom-24-regular'
          };
        case 'assignmentsQuestions':
          return {
            name: 'Assignment',
            icon: 'fluent:task-list-24-regular'
          };
        case 'notificationDetails':
          return {
            name: 'Notification Details',
            icon: 'fluent:alert-urgent-24-regular'
          };
        default:
          return {
            name: path,
            icon: 'fluent:document-24-regular'
          };
      }
    }
    
    return pathMap[path] || {
      name: path.charAt(0).toUpperCase() + path.slice(1),
      icon: 'fluent:document-24-regular'
    };
  };

  // Get the current page title
const getPageTitle = () => {
  if (pathnames.length === 0) return 'Dashboard';
    
    const lastSegment = pathnames[pathnames.length - 1];
    const secondLastSegment = pathnames[pathnames.length - 2];
    
    // Get classroom ID from query params if it exists
    const params = new URLSearchParams(location.search);
    const classroomId = params.get('classroomid');
    
    // Special cases for different paths
    if (pathnames.includes('assignmentsQuestions')) {
      return 'Assignment';
    } else if (secondLastSegment === 'AllClassroom') {
      return 'Classroom Dashboard';
    } else if (secondLastSegment === 'WatchCourse') {
      return 'Course Content';
    } else if (secondLastSegment === 'courses' && 
        (lastSegment === 'courseDetails' || lastSegment.includes('%') || lastSegment.includes('eyJ'))) {
    return 'Course Details';
    } else if (lastSegment === 'notificationDetails' || 
              (secondLastSegment === 'notifications' && (lastSegment.includes('%') || lastSegment.includes('eyJ')))) {
      return 'Notification Details';
    } else if (secondLastSegment === 'help') {
      if (lastSegment === 'faq') return 'FAQ';
      if (lastSegment === 'ticket') return 'Tickets';
    } else if (lastSegment === 'orderDetails') {
      return 'Order Details';
    } else if (lastSegment === 'orderDetailsWithPayu') {
      return 'Order Details';
    } else if (lastSegment === 'successPayment') {
      return 'Payment Successful';
    }
    
    return getPathInfo(lastSegment).name;
  };

  // Get breadcrumb items based on current path
  const getBreadcrumbItems = () => {
    const items = [
      {
        name: 'Dashboard',
        icon: 'fluent:home-24-regular',
        path: '/user/dashboard'
      }
    ];

    // Get classroom ID from query params if it exists
    const params = new URLSearchParams(location.search);
    const classroomId = params.get('classroomid');

    // Special handling for assignments questions with classroom context
    if (pathnames.includes('assignmentsQuestions') && classroomId) {
      return [
        ...items,
        {
          name: 'Classrooms',
          icon: 'fluent:class-24-regular',
          path: '/user/AllClassroom'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:classroom-24-regular',
          path: `/user/AllClassroom/classroomDetails/${classroomId}`
        },
        {
          name: 'Assignment',
          icon: 'fluent:task-list-24-regular',
          path: location.pathname + location.search
        }
      ];
    }

    // Special handling for classroom details
    if (pathnames.includes('classroomDetails') || 
        (pathnames.includes('AllClassroom') && 
         (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ')))) {
      return [
        ...items,
        {
          name: 'Classrooms',
          icon: 'fluent:class-24-regular',
          path: '/user/AllClassroom'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:classroom-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for success payment
    if (pathnames.includes('successPayment') || pathnames.includes('successPayUPayment')) {
      return [
        {
          name: 'Payment Successful',
          icon: 'fluent:checkmark-circle-24-regular',
          path: location.pathname + location.search
        }
      ];
    }

    // Special handling for order details
    if (pathnames.includes('orderDetails')) {
      return [
        ...items,
        {
          name: 'Courses',
          icon: 'fluent:book-24-regular',
          path: '/user/courses'
        },
        {
          name: 'Order Details',
          icon: 'fluent:receipt-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for order details with PayU
    if (pathnames.includes('orderDetailsWithPayu')) {
      return [
        ...items,
        {
          name: 'Courses',
          icon: 'fluent:book-24-regular',
          path: '/user/courses'
        },
        {
          name: 'Order Details',
          icon: 'fluent:receipt-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for help pages
    if (pathnames.includes('help')) {
      const helpItems = [
        {
          name: 'Help',
          icon: 'fluent:question-circle-24-regular',
          path: '/user/help'
        }
      ];

      // Add FAQ or Ticket based on the path
      if (pathnames.includes('faq')) {
        helpItems.push({
          name: 'FAQ',
          icon: 'fluent:book-question-mark-24-regular',
          path: location.pathname
        });
      } else if (pathnames.includes('ticket')) {
        helpItems.push({
          name: 'Tickets',
          icon: 'fluent:ticket-24-regular',
          path: location.pathname
        });
      }

      return [...items, ...helpItems];
    }

    // Special handling for notification details
    if (pathnames.includes('notificationDetails') || 
        (pathnames.includes('notifications') && pathnames[pathnames.length - 1].includes('%'))) {
      return [
        ...items,
        {
          name: 'Notifications',
          icon: 'fluent:alert-24-regular',
          path: '/user/notifications'
        },
        {
          name: 'Notification Details',
          icon: 'fluent:alert-urgent-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for my-feed
    if (pathnames.includes('my-feed')) {
      return [
        ...items,
        {
          name: 'Feed',
          icon: 'fluent:document-24-regular',
          path: '/user/feed'
        },
        {
          name: 'My Feed',
          icon: 'fluent:document-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for WatchCourse
    if (pathnames.includes('WatchCourse')) {
      return [
        ...items,
        {
          name: 'Courses',
          icon: 'fluent:book-24-regular',
          path: '/user/courses'
        },
        {
          name: 'Course Content',
          icon: 'fluent:book-open-24-regular',
          path: location.pathname
        }
      ];
    }

    // Special handling for course details
    if (pathnames.includes('courseDetails') || 
        (pathnames.includes('courses') && 
         (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ')))) {
      return [
        ...items,
        {
          name: 'Courses',
          icon: 'fluent:book-24-regular',
          path: '/user/courses'
        },
        {
          name: 'Course Details',
          icon: 'fluent:book-information-24-regular',
          path: location.pathname
        }
      ];
    }

    // Add current path item
    if (pathnames.length > 0) {
      const currentPath = pathnames[pathnames.length - 1];
      const pathInfo = getPathInfo(currentPath);
      
      items.push({
        name: pathInfo.name,
        icon: pathInfo.icon,
        path: location.pathname
      });
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  return (
    <nav aria-label="breadcrumb" className="bg-white py-3 px-4 mb-3 rounded shadow-sm">
      <div className="d-flex justify-content-between align-items-center">
        {/* Page Title with Back Button */}
        <div className="d-flex align-items-center">
          {pathnames.length > 0 && !pathnames.includes('successPayment') && !pathnames.includes('successPayUPayment') && (
            <button 
              onClick={handleBack}
              className="btn btn-icon btn-light rounded-circle me-2"
              style={{ 
                width: '32px', 
                height: '32px', 
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px solid #e0e0e0'
              }}
            >
              <Icon icon="fluent:arrow-left-24-regular" width="20" height="20" />
            </button>
          )}
          <h4 className="mb-0 fw-bold d-flex align-items-center">
            <Icon
              icon={getPathInfo(pathnames[pathnames.length - 1] || 'dashboard').icon}
              className="me-2"
              width="24"
              height="24"
            />
            {getPageTitle()}
          </h4>
        </div>

        {/* Breadcrumbs on the right */}
        <ol className="breadcrumb mb-0 d-flex align-items-center d-none d-md-flex">
          {breadcrumbItems.map((item, index) => (
            <React.Fragment key={item.path}>
              {index > 0 && (
                <li className="mx-1 text-muted">
                  <Icon icon="fluent:chevron-right-24-regular" width="16" height="16" />
                </li>
              )}
              <li className={`breadcrumb-item ${index === breadcrumbItems.length - 1 ? 'active fw-semibold' : ''}`}>
                {index === breadcrumbItems.length - 1 ? (
                    <span className="text-primary d-flex align-items-center">
                    <Icon icon={item.icon} className="me-1" width="18" height="18" />
                    {item.name}
                    </span>
                ) : (
                    <Link 
                    to={item.path}
                      className="text-decoration-none text-secondary d-flex align-items-center"
                    >
                    <Icon icon={item.icon} className="me-1" width="18" height="18" />
                    {item.name}
                    </Link>
                  )}
                  </li>
              </React.Fragment>
          ))}
        </ol>
      </div>
    </nav>
  );
}

export default Breadcrumbs;