// utils/encodeAndEncode.js

function encodeData(obj) {
  try {
    const jsonString = JSON.stringify(obj);
    const base64String = Buffer.from(jsonString, 'utf8').toString('base64');
    return encodeURIComponent(base64String);
  } catch (error) {
    console.error('Encoding failed:', error);
    return '';
  }
}

function decodeData(str) {
  try {
    const base64String = decodeURIComponent(str);
    const jsonString = Buffer.from(base64String, 'base64').toString('utf8');
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Decoding failed:', error);
    return null;
  }
}

module.exports = {
  encodeData,
  decodeData
};